# -*- coding: utf-8 -*-
"""
优化版推理模块 - 修复过度标点、标签不一致问题 & 强化后处理
"""
import os
import sys
import logging
import torch
import re
import json
import datetime
from config import PunctuationConfig


# 统一而安全的日志配置（避免外部库关闭句柄导致的错误）
logging.basicConfig(level=logging.INFO)
# 禁止日志系统在处理器I/O异常时抛出异常（如已关闭的文件句柄）
logging.raiseExceptions = False
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)
logger.propagate = False
if not logger.handlers:
    _sh = logging.StreamHandler(stream=sys.stdout)
    _sh.setLevel(logging.INFO)
    _sh.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(name)s - %(message)s'))
    logger.addHandler(_sh)


def _ensure_logging_handlers_open():
    """
    修复可能存在的已关闭日志处理器，避免 'I/O operation on closed file'。
    仅处理根日志器和常见的外部库日志器，不改变其它逻辑。
    """
    try:
        candidate_names = ["", "funasr", "modelscope", "huggingface_hub"]
        for name in candidate_names:
            lg = logging.getLogger(name) if name else logging.getLogger()
            handlers = list(getattr(lg, "handlers", []))
            changed = False
            for h in handlers:
                stream = getattr(h, "stream", None)
                # 如果是已关闭的流或为None（部分FileHandler关闭后为None），移除该处理器
                is_closed = False
                try:
                    is_closed = (stream is None) or (hasattr(stream, "closed") and getattr(stream, "closed", False))
                except Exception:
                    is_closed = False
                if is_closed:
                    try:
                        lg.removeHandler(h)
                        changed = True
                    except Exception:
                        pass
            # 根日志器兜底，确保至少有一个可用的输出处理器
            if (not lg.handlers) and (name == ""):
                try:
                    sh = logging.StreamHandler(stream=sys.stderr)
                    sh.setLevel(logging.INFO)
                    sh.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(name)s - %(message)s'))
                    lg.addHandler(sh)
                    lg.setLevel(logging.INFO)
                except Exception:
                    pass
            # 如果移除了坏处理器，确保该logger可用，并允许传播到根日志器
            if changed:
                try:
                    lg.disabled = False
                    if name:
                        lg.propagate = True
                except Exception:
                    pass
    except Exception:
        # 兜底：不让日志修复影响主流程
        pass

class PunctuationPredictor:
    """优化版标点符号预测器"""
    def __init__(self, model_path: str = None):
        # 确保日志处理器健康，防止后续外部库日志写入已关闭流
        _ensure_logging_handlers_open()

        self.config = PunctuationConfig()
        self.device = torch.device(self.config.DEVICE)
        self.use_finetuned = False
        self.am = None  # for pretrained models
        self.tokenizer = None
        self.model = None

        if self.config.has_finetuned_model():
            logger.info("发现微调后的模型，优先尝试加载微调模型...")
            try:
                self.tokenizer = self._load_simple_tokenizer(self.config.FINETUNED_MODEL_DIR)
                self.model = self._load_simple_model(self.config.FINETUNED_MODEL_DIR).to(self.device)
                self.model.eval()
                self.use_finetuned = True
                logger.info("✅ 微调模型加载成功")
                print(f"🎯 使用微调后的模型进行推理")
                print(f"📱 使用设备: {self.device}")
            except Exception as e:
                logger.warning(f"微调模型加载失败，回退到预训练模型: {e}")
                self._load_pretrained_model()
        else:
            logger.info("未找到微调模型，加载预训练模型进行推理...")
            self._load_pretrained_model()

    def _load_pretrained_model(self):
        # 在调用外部库前，确保日志处理器未被关闭
        _ensure_logging_handlers_open()
        try:
            from funasr import AutoModel
            local_cache_dir = os.path.join(os.getcwd(), "models", "pretrained")
            os.makedirs(local_cache_dir, exist_ok=True)
            self.am = AutoModel(
                model=self.config.PRETRAINED_MODEL_NAME,
                model_revision=self.config.MODEL_REVISION,
                disable_update=True,
                device=self.device.type,
                cache_dir=local_cache_dir
            )
            self.use_finetuned = False
            logger.info("✅ CT-Transformer预训练模型加载成功")
            print(f"🔧 使用预训练模型进行推理")
            print(f"📱 使用设备: {self.device}")
        except ImportError:
            logger.error("❌ 缺少funasr依赖，请安装: pip install funasr")
            raise
        except Exception as e:
            logger.error(f"❌ CT-Transformer模型加载失败: {e}")
            raise

    def _load_simple_tokenizer(self, model_dir):
        """
        优先加载训练阶段保存的 tokenizer_config.json，以保证训练/推理端编码一致；
        若不存在或解析失败，则回退到内置的 SimpleTokenizer。
        支持类型：CTTokenizerFromPuncList、CTCompatibleTokenizer。
        """
        try:
            cfg_path = os.path.join(model_dir, 'tokenizer_config.json')
            if os.path.exists(cfg_path):
                import json
                with open(cfg_path, 'r', encoding='utf-8') as f:
                    cfg = json.load(f)
                ttype = cfg.get('tokenizer_type', '')

                if ttype == 'CTTokenizerFromPuncList':
                    class CTTokenizerFromPuncList:
                        def __init__(self, cfg):
                            # 读取配置
                            self.pad_token = cfg.get('pad_token', '[PAD]')
                            self.cls_token = cfg.get('cls_token', '[CLS]')
                            self.sep_token = cfg.get('sep_token', '[SEP]')
                            self.unk_token = cfg.get('unk_token', '[UNK]')
                            self.vocab_size = int(cfg.get('vocab_size', 272727))
                            self.special_tokens = cfg.get('special_tokens') or {
                                self.pad_token: 0,
                                self.unk_token: 1,
                                self.cls_token: 101,
                                self.sep_token: 102,
                            }
                            # 标点映射优先读取 punc_mapping；若缺失则由 normalized_puncs 重建
                            mapping = cfg.get('punc_mapping') or {}
                            normalized = cfg.get('normalized_puncs') or []
                            if not mapping and normalized:
                                base_id = 200
                                mapping = {p: base_id + i for i, p in enumerate(normalized)}
                            self.punc_mapping = mapping
                            # 记录原始/规范化标点（可用于调试）
                            self.punc_list = cfg.get('punc_list', [])
                            self.normalized_puncs = normalized

                        def tokenize(self, text):
                            return list(str(text)) if text else []

                        def _char_to_id(self, char):
                            if char in self.special_tokens:
                                return self.special_tokens[char]
                            if char in self.punc_mapping:
                                return self.punc_mapping[char]
                            # 与训练侧一致的回退映射：将字符编码压入安全范围
                            char_code = ord(char)
                            return min(max(char_code, 1000), self.vocab_size - 1)

                        def __call__(self, text, truncation=True, padding='max_length', max_length=512, return_tensors='pt'):
                            tokens = [self.cls_token] + self.tokenize(text)[:max_length-2] + [self.sep_token]
                            input_ids = [self._char_to_id(tok) for tok in tokens]
                            if len(input_ids) < max_length:
                                input_ids.extend([self.special_tokens[self.pad_token]] * (max_length - len(input_ids)))
                            else:
                                input_ids = input_ids[:max_length]
                            attention_mask = [1 if i != self.special_tokens[self.pad_token] else 0 for i in input_ids]
                            import torch
                            return {
                                'input_ids': torch.tensor([input_ids], dtype=torch.long),
                                'attention_mask': torch.tensor([attention_mask], dtype=torch.long)
                            }

                        def save_pretrained(self, path):
                            import json, os
                            os.makedirs(path, exist_ok=True)
                            config = {
                                'tokenizer_type': 'CTTokenizerFromPuncList',
                                'pad_token': self.pad_token,
                                'cls_token': self.cls_token,
                                'sep_token': self.sep_token,
                                'unk_token': self.unk_token,
                                'vocab_size': self.vocab_size,
                                'special_tokens': self.special_tokens,
                                'punc_mapping': self.punc_mapping,
                                'punc_list': self.punc_list,
                                'normalized_puncs': self.normalized_puncs,
                            }
                            with open(os.path.join(path, 'tokenizer_config.json'), 'w', encoding='utf-8') as f:
                                json.dump(config, f, ensure_ascii=False, indent=2)

                    logger.info("✅ 加载训练阶段保存的 tokenizer: CTTokenizerFromPuncList")
                    return CTTokenizerFromPuncList(cfg)

                if ttype == 'CTCompatibleTokenizer':
                    class CTCompatibleTokenizer:
                        def __init__(self, cfg):
                            self.pad_token = cfg.get('pad_token', '[PAD]')
                            self.cls_token = cfg.get('cls_token', '[CLS]')
                            self.sep_token = cfg.get('sep_token', '[SEP]')
                            self.unk_token = cfg.get('unk_token', '[UNK]')
                            self.vocab_size = int(cfg.get('vocab_size', 272727))
                            self.special_tokens = cfg.get('special_tokens') or {
                                self.pad_token: 0,
                                self.unk_token: 1,
                                self.cls_token: 101,
                                self.sep_token: 102,
                            }

                        def tokenize(self, text):
                            return list(str(text)) if text else []

                        def _char_to_id(self, char):
                            # 与训练侧 CTCompatibleTokenizer 一致的映射规则
                            if char in self.special_tokens:
                                return self.special_tokens[char]
                            try:
                                char_code = ord(char)
                                if 0x4e00 <= char_code <= 0x9fff:      # 中文
                                    char_id = char_code - 0x4e00 + 1000
                                elif 0x0030 <= char_code <= 0x0039:    # 数字
                                    char_id = char_code - 0x0030 + 500
                                elif 0x0041 <= char_code <= 0x005a:    # 大写字母
                                    char_id = char_code - 0x0041 + 600
                                elif 0x0061 <= char_code <= 0x007a:    # 小写字母
                                    char_id = char_code - 0x0061 + 650
                                elif char in '，。？！、；：""\'\'（）【】':  # 常见标点
                                    char_id = 700 + ord(char) % 100
                                else:
                                    char_id = (char_code % (self.vocab_size - 1000)) + 1000
                                return min(max(char_id, 103), self.vocab_size - 1)
                            except Exception:
                                return self.special_tokens[self.unk_token]

                        def __call__(self, text, truncation=True, padding='max_length', max_length=512, return_tensors='pt'):
                            tokens = [self.cls_token] + self.tokenize(text)[:max_length-2] + [self.sep_token]
                            input_ids = []
                            for tok in tokens:
                                token_id = self._char_to_id(tok)
                                if token_id >= self.vocab_size:
                                    token_id = self.special_tokens[self.unk_token]
                                input_ids.append(token_id)
                            while len(input_ids) < max_length:
                                input_ids.append(self.special_tokens[self.pad_token])
                            attention_mask = [1 if i != self.special_tokens[self.pad_token] else 0 for i in input_ids]
                            import torch
                            return {
                                'input_ids': torch.tensor([input_ids], dtype=torch.long),
                                'attention_mask': torch.tensor([attention_mask], dtype=torch.long)
                            }

                        def save_pretrained(self, path):
                            import json, os
                            os.makedirs(path, exist_ok=True)
                            config = {
                                'tokenizer_type': 'CTCompatibleTokenizer',
                                'pad_token': self.pad_token,
                                'cls_token': self.cls_token,
                                'sep_token': self.sep_token,
                                'unk_token': self.unk_token,
                                'vocab_size': self.vocab_size,
                                'special_tokens': self.special_tokens,
                            }
                            with open(os.path.join(path, 'tokenizer_config.json'), 'w', encoding='utf-8') as f:
                                json.dump(config, f, ensure_ascii=False, indent=2)

                    logger.info("✅ 加载训练阶段保存的 tokenizer: CTCompatibleTokenizer")
                    return CTCompatibleTokenizer(cfg)

                logger.warning(f"未识别的 tokenizer_type: {ttype}，回退到 SimpleTokenizer")
        except Exception as e:
            logger.warning(f"加载训练保存的 tokenizer 配置失败: {e}，回退到 SimpleTokenizer")

        # 回退：内置的 SimpleTokenizer（向后兼容）
        class SimpleTokenizer:
            def __init__(self):
                self.pad_token = '[PAD]'
                self.cls_token = '[CLS]'
                self.sep_token = '[SEP]'
                self.unk_token = '[UNK]'
                self.vocab_size = 21128
                self.special_tokens = {
                    self.pad_token: 0,
                    self.unk_token: 1,
                    self.cls_token: 101,
                    self.sep_token: 102
                }

            def tokenize(self, text):
                return list(text)

            def _char_to_id(self, char):
                if char in self.special_tokens:
                    return self.special_tokens[char]
                char_code = ord(char)
                char_id = (char_code % (self.vocab_size - 103)) + 103
                return min(max(char_id, 103), self.vocab_size - 1)

            def __call__(self, text, truncation=True, padding='max_length', max_length=512, return_tensors='pt'):
                tokens = [self.cls_token] + self.tokenize(text)[:max_length-2] + [self.sep_token]
                input_ids = []
                for token in tokens:
                    token_id = self._char_to_id(token)
                    if token_id >= self.vocab_size:
                        token_id = self.special_tokens[self.unk_token]
                    input_ids.append(token_id)
                while len(input_ids) < max_length:
                    input_ids.append(self.special_tokens[self.pad_token])
                attention_mask = [1 if id != self.special_tokens[self.pad_token] else 0 for id in input_ids]
                if return_tensors == 'pt':
                    return {
                        'input_ids': torch.tensor([input_ids], dtype=torch.long),
                        'attention_mask': torch.tensor([attention_mask], dtype=torch.long)
                    }
                return {'input_ids': input_ids, 'attention_mask': attention_mask}

            def save_pretrained(self, path):
                import json
                config = {
                    'tokenizer_type': 'SimpleTokenizer',
                    'pad_token': self.pad_token,
                    'cls_token': self.cls_token,
                    'sep_token': self.sep_token,
                    'unk_token': self.unk_token
                }
                with open(os.path.join(path, 'tokenizer_config.json'), 'w', encoding='utf-8') as f:
                    json.dump(config, f, ensure_ascii=False, indent=2)

        return SimpleTokenizer()

    def _load_simple_model(self, model_dir):
        """加载微调后的模型"""
        import torch.nn as nn
        import json

        # 首先检查模型配置
        config_path = os.path.join(model_dir, 'config.json')
        if os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8') as f:
                model_config = json.load(f)
            model_type = model_config.get('model_type', 'SimpleClassificationModel')
        else:
            model_type = 'SimpleClassificationModel'

        if model_type == 'CTTransformerForPunctuation':
            # 加载基于CT-Transformer的模型
            return self._load_ct_based_model(model_dir)
        else:
            # 加载简单分类模型（向后兼容）
            return self._load_legacy_model(model_dir)

    def _load_ct_based_model(self, model_dir):
        """加载基于CT-Transformer的微调模型"""
        # 在调用外部库前，确保日志处理器未被关闭
        _ensure_logging_handlers_open()
        try:
            from funasr import AutoModel
            import torch.nn as nn

            # 重新加载CT-Transformer基础模型
            local_cache_dir = os.path.join(os.getcwd(), "models", "pretrained")
            ct_model = AutoModel(
                model="damo/punc_ct-transformer_zh-cn-common-vocab272727-pytorch",
                model_revision="v2.0.4",
                disable_update=True,
                device=self.device.type,
                cache_dir=local_cache_dir
            )

            # 读取训练时保存的结构参数，以确保推理结构与训练一致
            model_cfg_path = os.path.join(model_dir, 'config.json')
            _hidden_size = 768
            _classifier_arch = 'two_layer'
            _dropout_p = 0.3
            try:
                if os.path.exists(model_cfg_path):
                    with open(model_cfg_path, 'r', encoding='utf-8') as _f:
                        _mcfg = json.load(_f)
                    _hidden_size = int(_mcfg.get('hidden_size', _hidden_size))
                    _classifier_arch = str(_mcfg.get('classifier_arch', _classifier_arch))
                    _dropout_p = float(_mcfg.get('dropout_p', _dropout_p))
            except Exception:
                pass

            # 重建模型结构（与训练时保持一致）：使用与训练端相同的CT特征提取/回退逻辑
            class CTTransformerForPunctuation(nn.Module):
                def __init__(self, ct_model, num_labels=6, hidden_size=768, classifier_arch='two_layer', dropout_p=0.3):
                    super().__init__()
                    self.ct_model = ct_model
                    self.num_labels = num_labels
                    self.hidden_size = hidden_size
                    self.classifier_arch = classifier_arch
                    self.dropout = nn.Dropout(dropout_p)

                    if self.classifier_arch == 'two_layer':
                        self.classifier = nn.Sequential(
                            nn.Linear(hidden_size, hidden_size // 2),
                            nn.ReLU(),
                            nn.Dropout(dropout_p),
                            nn.Linear(hidden_size // 2, num_labels)
                        )
                    else:
                        self.classifier = nn.Linear(hidden_size, num_labels)

                    # 仅记录一次可能的编码器模块（与训练端风格保持）
                    self._ct_encoder_modules_logged = False

                def forward(self, input_ids, attention_mask=None, labels=None):
                    try:
                        batch_size, seq_len = input_ids.shape

                        # 路径1：尝试通过hook捕获CT-Transformer编码器隐层
                        hidden_states = self._extract_ct_features(input_ids, batch_size, seq_len)

                        # 路径2：回退到基于嵌入的特征
                        if hidden_states is None:
                            hidden_states = self._create_embedding_features(input_ids, batch_size, seq_len)

                        hidden_states = self.dropout(hidden_states)
                        logits = self.classifier(hidden_states)

                        class ModelOutput:
                            def __init__(self, loss, logits):
                                self.loss = loss
                                self.logits = logits
                        return ModelOutput(None, logits)
                    except Exception as e:
                        logger.error(f"推理前向传播失败: {e}")
                        batch_size, seq_len = input_ids.shape
                        hidden_states = torch.randn(batch_size, seq_len, self.hidden_size, device=input_ids.device)
                        logits = self.classifier(hidden_states)
                        class ModelOutput:
                            def __init__(self, loss, logits):
                                self.loss = loss
                                self.logits = logits
                        return ModelOutput(None, logits)

                def _extract_ct_features(self, input_ids, batch_size, seq_len):
                    """与训练端一致的CT特征提取：对每个样本通过hook捕获编码器隐层，失败则返回None"""
                    try:
                        input_texts = self._ids_to_texts(input_ids)
                        ct_outputs = []
                        for text in input_texts:
                            feats = self._get_hidden_by_hook(text, seq_len, input_ids.device)
                            if feats is None:
                                return None  # 如无法稳定捕获，整体回退到嵌入路径，确保分布一致
                            ct_outputs.append(feats)
                        if ct_outputs:
                            return torch.stack(ct_outputs, dim=0)
                        return None
                    except Exception:
                        return None

                def _get_hidden_by_hook(self, text: str, seq_len: int, device: torch.device):
                    core = getattr(self.ct_model, 'model', None)
                    if core is None:
                        return None
                    prefer = ['encoder', 'backbone', 'punc', 'punct', 'transformer']
                    modules = []
                    for name, mod in core.named_modules():
                        if any(k in name.lower() for k in prefer):
                            modules.append((name, mod))
                    captured = {}

                    def make_hook(key):
                        def hook(_m, _inp, out):
                            captured[key] = out
                        return hook

                    for name, mod in modules[:20]:
                        handle = None
                        try:
                            handle = mod.register_forward_hook(make_hook(name))
                            # 触发一次CT模型前向（抑制外部库输出）
                            import os
                            from contextlib import redirect_stdout, redirect_stderr
                            with open(os.devnull, 'w') as devnull:
                                with redirect_stdout(devnull), redirect_stderr(devnull):
                                    _ = self.ct_model.generate(input=text)
                            out = captured.get(name, None)
                            if out is None:
                                continue
                            if isinstance(out, (list, tuple)):
                                out = out[-1]
                            if isinstance(out, dict):
                                out = out.get('hidden_states', None) or out.get('last_hidden_state', None)
                            if not isinstance(out, torch.Tensor):
                                continue
                            # 期望 [T,H] 或 [B,T,H]
                            if out.dim() == 2:
                                feats = out
                            elif out.dim() == 3:
                                feats = out[0]
                            else:
                                continue
                            # 线性投影到 hidden_size（如需要）
                            if feats.size(-1) != self.hidden_size:
                                proj = torch.nn.Linear(feats.size(-1), self.hidden_size, bias=False).to(feats.device)
                                with torch.no_grad():
                                    feats = proj(feats)
                            # 对齐序列长度（保留CLS/SEP位置）
                            T, H = feats.shape[-2], feats.shape[-1]
                            use_T = min(T, seq_len)
                            feats = feats[:use_T, :]
                            if use_T < seq_len:
                                pad = torch.zeros(seq_len - use_T, H, device=feats.device, dtype=feats.dtype)
                                feats = torch.cat([feats, pad], dim=0)
                            return feats.to(device)
                        except Exception:
                            pass
                        finally:
                            if handle is not None:
                                handle.remove()
                    return None

                def _ids_to_texts(self, input_ids):
                    input_texts = []
                    for i in range(input_ids.shape[0]):
                        text_chars = []
                        for token_id in input_ids[i]:
                            token_id = int(token_id.item())
                            if token_id == 0:  # PAD
                                break
                            elif token_id == 101:  # CLS
                                continue
                            elif token_id == 102:  # SEP
                                break
                            else:
                                if 1000 <= token_id <= 22000:
                                    char_code = token_id - 1000 + 0x4e00
                                    if 0x4e00 <= char_code <= 0x9fff:
                                        text_chars.append(chr(char_code))
                                    else:
                                        text_chars.append('？')
                                elif 500 <= token_id <= 509:
                                    text_chars.append(str(token_id - 500))
                                elif 600 <= token_id <= 625:
                                    text_chars.append(chr(token_id - 600 + ord('A')))
                                elif 650 <= token_id <= 675:
                                    text_chars.append(chr(token_id - 650 + ord('a')))
                                else:
                                    text_chars.append('？')
                        input_texts.append(''.join(text_chars))
                    return input_texts

                def _create_embedding_features(self, input_ids, batch_size, seq_len):
                    if not hasattr(self, 'embedding'):
                        vocab_size = 272727
                        self.embedding = nn.Embedding(vocab_size, self.hidden_size, padding_idx=0)
                        self.embedding = self.embedding.to(input_ids.device)
                    input_ids_clamped = torch.clamp(input_ids, 0, 272726)
                    return self.embedding(input_ids_clamped)

            model = CTTransformerForPunctuation(
                ct_model,
                num_labels=len(self.config.LABELS),
                hidden_size=_hidden_size,
                classifier_arch=_classifier_arch,
                dropout_p=_dropout_p,
            )

            # 加载权重
            weights_path = os.path.join(model_dir, 'pytorch_model.bin')
            if os.path.exists(weights_path):
                state_dict = torch.load(weights_path, map_location=self.device)
                missing, unexpected = model.load_state_dict(state_dict, strict=False)
                logger.info("✅ 成功加载CT-Transformer微调模型")
            else:
                logger.warning("⚠️ 未找到模型权重文件，使用随机初始化")

            return model

        except Exception as e:
            logger.error(f"加载CT-Transformer模型失败: {e}")
            import traceback
            traceback.print_exc()
            return self._load_legacy_model(model_dir)

    def _load_legacy_model(self, model_dir):
        """加载传统简单分类模型"""
        import torch.nn as nn

        class SimpleClassificationModel(nn.Module):
            def __init__(self, vocab_size=21128, hidden_size=768, num_labels=6):
                super().__init__()
                self.vocab_size = vocab_size
                self.embedding = nn.Embedding(vocab_size, hidden_size, padding_idx=0)
                self.transformer = nn.TransformerEncoder(
                    nn.TransformerEncoderLayer(hidden_size, nhead=8, batch_first=True),
                    num_layers=12
                )
                self.classifier = nn.Linear(hidden_size, num_labels)
                self.dropout = nn.Dropout(0.1)

            def forward(self, input_ids, attention_mask=None, labels=None):
                if torch.any(input_ids >= self.vocab_size) or torch.any(input_ids < 0):
                    invalid_indices = input_ids[(input_ids >= self.vocab_size) | (input_ids < 0)]
                    raise ValueError(f"Invalid token IDs: {invalid_indices.tolist()}")
                embeddings = self.embedding(input_ids)
                mask = attention_mask == 0 if attention_mask is not None else None
                hidden_states = self.transformer(embeddings, src_key_padding_mask=mask)
                hidden_states = self.dropout(hidden_states)
                logits = self.classifier(hidden_states)

                class ModelOutput:
                    def __init__(self, loss, logits):
                        self.loss = loss
                        self.logits = logits
                return ModelOutput(None, logits)

        model = SimpleClassificationModel(num_labels=len(self.config.LABELS))
        weights_path = os.path.join(model_dir, 'pytorch_model.bin')
        if os.path.exists(weights_path):
            model.load_state_dict(torch.load(weights_path, map_location=self.device))
        return model

    def predict(self, text: str) -> str:
        if not text or not text.strip():
            return text
        if self.use_finetuned and self.model is not None and self.tokenizer is not None:
            return self._predict_with_finetuned_model(text)
        elif self.am is not None:
            return self._predict_with_pretrained_model(text)
        else:
            logger.warning("无可用模型，返回原文")
            return text

    def _predict_with_finetuned_model(self, text: str) -> str:
        """
        使用微调模型进行预测，采用与预训练模型一致的简化解码策略
        """
        try:
            clean_text = self._remove_punctuation(text)
            if not clean_text.strip():
                logger.warning("清理后的文本为空，返回原文")
                return text

            encoding = self.tokenizer(
                clean_text,
                truncation=True,
                padding='max_length',
                max_length=self.config.MAX_LENGTH,
                return_tensors='pt'
            )
            with torch.no_grad():
                input_ids = encoding['input_ids'].to(self.device)
                attention_mask = encoding['attention_mask'].to(self.device)
                outputs = self.model(input_ids=input_ids, attention_mask=attention_mask)
                logits = outputs.logits  # [batch, seq, num_labels]
                predictions = torch.argmax(logits, dim=-1)  # 直接取最大概率标签，与预训练模型保持一致

            # 使用简化的解码策略，与预训练模型保持一致
            result = self._decode_predictions_simple(
                clean_text,
                predictions[0].cpu(),
                attention_mask=attention_mask[0].cpu()
            )
            logger.info(f"微调模型预测: {text} -> {result}")
            return result
        except Exception as e:
            logger.error(f"微调模型预测失败: {e}")
            return text

    def _predict_with_pretrained_model(self, text: str) -> str:
        """
        使用预训练模型进行预测，确保与微调模型使用相同的解码策略
        """
        try:
            # 移除输入文本中的标点符号，确保与微调模型输入一致
            clean_text = self._remove_punctuation(text)
            if not clean_text.strip():
                logger.warning("清理后的文本为空，返回原文")
                return text

            # 使用预训练模型进行预测
            result = self.am.generate(input=clean_text)
            if result and len(result) > 0:
                predicted_text = result[0].get('text', clean_text)
                logger.info(f"预训练模型预测: {text} -> {predicted_text}")
                return predicted_text
            else:
                logger.warning("预训练模型预测结果为空，返回原文")
                return text
        except Exception as e:
            logger.error(f"预训练模型预测失败: {e}")
            return text

    def _remove_punctuation(self, text: str) -> str:
        punctuations = ['，', '。', '？', '！', '、', '；', '：', '"', "'", '“', '”', '’', '‘']
        clean_text = text
        for punct in punctuations:
            clean_text = clean_text.replace(punct, '')
        return clean_text

    def _decode_predictions_simple(self, text: str, predictions: torch.Tensor, attention_mask: torch.Tensor = None) -> str:
        """
        简化的解码方法，与预训练模型保持一致的标点插入策略
        添加合理的间隔控制，避免过度标点
        - predictions: [seq]
        - attention_mask: [seq]（可选），用于跳过 PAD 位置
        """
        result = []
        pred_idx = 1  # 跳过 CLS
        last_punct_pos = -10  # 最近一次插入标点的位置，初始化为较小值
        min_gap = 8  # 增加最小标点间隔，从3增加到8，大幅减少标点密度

        for i, char in enumerate(text):
            result.append(char)

            # 检查是否超出预测范围或遇到PAD
            if pred_idx >= len(predictions):
                break
            if attention_mask is not None and attention_mask[pred_idx] == 0:
                pred_idx += 1
                continue

            pred_label = predictions[pred_idx].item()

            # 只有在满足最小间隔要求时才考虑插入标点
            if i - last_punct_pos >= min_gap:
                # 添加更严格的标点插入条件
                should_insert_punct = False
                punct_to_insert = ""

                # 根据标签插入标点，使用简单的映射策略
                if pred_label == self.config.LABEL_TO_ID.get('COMMA', 1):
                    # 额外检查：避免在数字串中插入逗号，并且只在合适的位置插入
                    if not self._is_in_number_sequence(text, i) and self._is_suitable_for_comma(text, i):
                        should_insert_punct = True
                        punct_to_insert = '，'
                elif pred_label == self.config.LABEL_TO_ID.get('PERIOD', 2):
                    # 句号插入更加保守，只在句子结尾或长句中间插入
                    if self._is_suitable_for_period(text, i):
                        should_insert_punct = True
                        punct_to_insert = '。'
                elif pred_label == self.config.LABEL_TO_ID.get('QUESTION', 3):
                    # 问号检测：检查是否为疑问句
                    if self._is_question_context(text, i):
                        should_insert_punct = True
                        punct_to_insert = '？'
                elif pred_label == self.config.LABEL_TO_ID.get('EXCLAMATION', 4):
                    # 感叹号插入
                    if self._is_suitable_for_exclamation(text, i):
                        should_insert_punct = True
                        punct_to_insert = '！'

                # 执行标点插入
                if should_insert_punct:
                    result.append(punct_to_insert)
                    last_punct_pos = i
                # O标签不插入标点

            pred_idx += 1

        return ''.join(result)

    def _is_in_number_sequence(self, text: str, pos: int) -> bool:
        """
        检查指定位置是否在数字序列中，避免在数字串中插入不当标点
        """
        # 检查前后各5个字符是否包含数字（扩大检查范围）
        start = max(0, pos - 5)
        end = min(len(text), pos + 6)
        segment = text[start:end]

        # 数字相关字符
        number_chars = set('零一二三四五六七八九十百千万亿0123456789')

        # 如果段落中数字字符占比超过40%，认为是数字序列（降低阈值，更保守）
        digit_count = sum(1 for c in segment if c in number_chars)
        return digit_count / len(segment) > 0.4 if segment else False

    def _is_suitable_for_comma(self, text: str, pos: int) -> bool:
        """
        判断当前位置是否适合插入逗号
        """
        # 检查前后文本，避免在不合适的地方插入逗号
        if pos < 5 or pos >= len(text) - 2:  # 太靠近开头或结尾
            return False

        # 检查前面的字符，避免在某些词汇中间插入
        prev_chars = text[max(0, pos-3):pos+1]
        next_chars = text[pos:min(len(text), pos+4)]

        # 避免在常见的连续词汇中插入逗号
        avoid_patterns = ['作业', '时间', '号码', '工区', '线路', '车站', '项目', '人员', '防护']
        for pattern in avoid_patterns:
            if pattern in prev_chars + next_chars:
                return False

        return True

    def _is_suitable_for_period(self, text: str, pos: int) -> bool:
        """
        判断当前位置是否适合插入句号
        """
        # 只在句子相对较长且接近结尾时插入句号
        remaining_text = text[pos:]
        if len(remaining_text) > 20:  # 如果后面还有很多字符，不插入句号
            return False

        # 检查是否包含结束性词汇
        end_indicators = ['完毕', '结束', '正确', '明白', '收到', '一致']
        prev_text = text[max(0, pos-10):pos+1]
        for indicator in end_indicators:
            if indicator in prev_text:
                return True

        return len(remaining_text) <= 5  # 只在接近文本结尾时插入

    def _is_question_context(self, text: str, pos: int) -> bool:
        """
        判断当前位置是否为疑问句上下文
        """
        # 检查疑问词
        question_words = ['什么', '哪里', '怎么', '为什么', '是否', '有没有', '几点', '多少', '正确', '一致']

        # 检查前面的文本是否包含疑问词
        prev_text = text[:pos+1]
        for word in question_words:
            if word in prev_text:
                return True

        # 检查句子结构，如果接近结尾且包含疑问特征
        remaining_text = text[pos:]
        if len(remaining_text) <= 3:  # 接近结尾
            return any(word in prev_text for word in question_words)

        return False

    def _is_suitable_for_exclamation(self, text: str, pos: int) -> bool:
        """
        判断当前位置是否适合插入感叹号
        """
        # 检查感叹词
        exclamation_words = ['啊', '呃', '哎', '好', '明白']
        prev_text = text[max(0, pos-5):pos+1]

        for word in exclamation_words:
            if word in prev_text:
                return True

        return False

    def _decode_predictions(self, text: str, predictions: torch.Tensor, probs: torch.Tensor = None, attention_mask: torch.Tensor = None) -> str:
        """
        将标签解码为文本，参考预训练模型的解码策略，更合理地插入标点。
        - predictions: [seq]
        - probs: [seq, num_labels]（可选）
        - attention_mask: [seq]（可选），用于跳过 PAD 位置
        """
        result = []
        pred_idx = 1  # 跳过 CLS
        L = len(predictions)
        last_punct_pos = -100  # 最近一次插入标点对应的字符位置
        min_gap = 3            # 增加最小间隔，参考预训练模型的标点密度

        # 调整概率阈值，使其更接近预训练模型的标点密度
        o_id = self.config.LABEL_TO_ID.get('O', 0)
        min_conf = 0.4         # 降低置信度要求，允许更多标点
        min_margin = 0.15      # 降低边际要求
        end_min_conf = 0.5     # 句末标点置信度要求
        end_min_margin = 0.2   # 句末标点边际要求

        for i, char in enumerate(text):
            result.append(char)
            if pred_idx >= L:
                continue
            # attention_mask 对齐：pred_idx 对应当前字符（已跳过CLS）
            if attention_mask is not None:
                try:
                    if int(attention_mask[pred_idx].item()) == 0:
                        pred_idx += 1
                        continue
                except Exception:
                    pass

            label_id = int(predictions[pred_idx].item())
            pred_idx += 1
            if label_id >= len(self.config.LABELS):
                continue

            label = self.config.LABELS[label_id]
            punct = self.config.LABEL_TO_PUNCT.get(label, '')
            if not punct:
                continue

            # 概率抑制逻辑：若提供了 probs，则需要达到一定置信度和边际
            allow_insert = True
            if probs is not None and probs.shape[0] > pred_idx - 1:
                try:
                    p_vec = probs[pred_idx - 1]
                    p_label = float(p_vec[label_id].item())
                    p_o = float(p_vec[o_id].item()) if o_id < p_vec.shape[-1] else 0.0
                    margin = p_label - p_o
                    # 句中逗号/顿号用主阈值，句末标点用更高阈值
                    if punct in ['，', '、']:
                        allow_insert = (p_label >= min_conf) and (margin >= min_margin)
                    elif punct in ['。', '？', '！']:
                        allow_insert = (p_label >= end_min_conf) and (margin >= end_min_margin)
                    else:
                        allow_insert = (p_label >= min_conf) and (margin >= min_margin)
                except Exception:
                    allow_insert = True

            if not allow_insert:
                continue

            # 避免重复/过密插入
            if len(result) > 0 and result[-1] == punct:
                continue
            if i - last_punct_pos < min_gap:
                continue

            # 细粒度规则 - 更宽松的标点插入策略
            if punct == '、':
                if self._should_use_pause_enhanced(text, i):
                    result.append(punct)
                    last_punct_pos = i
                continue
            if punct == '，':
                if self._should_use_comma_enhanced(text, i):
                    result.append(punct)
                    last_punct_pos = i
                continue
            if punct in ['。', '？', '！']:
                if i == len(text) - 1 or self._is_sentence_boundary(text, i):
                    result.append(punct)
                    last_punct_pos = i
                else:
                    # 在句中位置，如果是句号则考虑转为逗号
                    if self._should_use_comma_enhanced(text, i) and punct == '。':
                        result.append('，')
                        last_punct_pos = i

        joined = ''.join(result)
        joined = self._final_punct_correction(joined, text)
        return self._post_process_punctuation(joined)

    def _is_sentence_boundary(self, text, idx):
        # 简单规则：下一个字符是否是大写/数字/停顿词或后一段较长非标点（保守判断）
        if idx + 1 >= len(text):
            return True
        next_char = text[idx+1]
        if re.match(r'[\u4e00-\u9fa5]', next_char):
            # 若下一个字符是连接词（如“和”“与”“及”）则非边界
            if next_char in ['和', '与', '及', '或', '和且', '并']:
                return False
        return False

    def _should_use_pause(self, text: str, idx: int) -> bool:
        """
        判断是否应使用顿号：更保守，特别针对铁路通信场景
        - 禁止在数字/时间/里程/代号串中使用（如 二零二五、K7+500、W0808 等）
        - 禁止在人名、地名、设备代号中使用
        - 仅在明显的并列短词边界处使用
        """
        if idx + 1 >= len(text):
            return False

        # 扩大检查范围，更严格的数字/代号检测
        around = text[max(0, idx-6): idx+7]

        # 禁止在各种数字串中使用顿号
        if re.search(r'[0-9零一二三四五六七八九十幺]{2,}', around):
            return False
        if re.search(r'K\s*[0-9零一二三四五六七八九十幺]+\s*[+加]\s*[0-9零一二三四五六七八九十幺]+', around):
            return False
        if re.search(r'[A-Za-z][0-9零一二三四五六七八九十幺]{2,}', around):
            return False

        # 禁止在时间表达式中使用
        if re.search(r'[0-9零一二三四五六七八九十幺]+点[0-9零一二三四五六七八九十幺]*', around):
            return False
        if re.search(r'[0-9零一二三四五六七八九十幺]+年[0-9零一二三四五六七八九十幺]*月', around):
            return False

        # 禁止在设备代号、工区名称中使用
        if re.search(r'[A-Za-z]+[0-9零一二三四五六七八九十幺]+', around):
            return False
        if re.search(r'(线路工区|车间|项目部|集团)', around):
            return False

        # 禁止在人名附近使用（常见姓氏+名字模式）
        common_surnames = ['董', '刘', '黄', '陆', '韦', '谢', '李', '王', '张', '陈', '杨', '赵', '孙', '周', '吴', '郑', '冯', '陶']
        for surname in common_surnames:
            if surname in around:
                return False

        # 向后看最多 3 个汉字；向前看最多 3 个汉字
        next_chunk = text[idx+1: idx+1+3]
        prev_chunk = text[max(0, idx-3):idx+1]
        if re.match(r'^[\u4e00-\u9fa5]{1,3}$', next_chunk) and re.match(r'^[\u4e00-\u9fa5]{1,3}$', prev_chunk):
            # 避免在介词/连词后立即加顿号
            if prev_chunk[-1] in ['和', '与', '及', '或', '并', '把', '将', '把']:
                return False
            # 只在明确的并列词汇处使用（如"苹果香蕉橙子"）
            if len(prev_chunk) >= 2 and len(next_chunk) >= 2:
                return True
        return False

    def _should_use_comma(self, text: str, idx: int) -> bool:
        """
        更保守的逗号插入规则，特别针对铁路通信场景：
        - 避免在短语内（短于等于2字符的左右两边）插逗号
        - 严格禁止在数字串、代号串内插逗号
        - 在长定语/从句处允许插入
        - 在人名、时间、地点等关键信息分隔处适当插入
        """
        start = max(0, idx-6)
        end = min(len(text), idx+7)
        segment = text[start:end]
        if len(segment) < 3:
            return False

        # 严格的数字串检查 - 禁止在任何数字序列中插入逗号
        if re.search(r'[0-9零一二三四五六七八九十幺]{2,}', segment):
            return False

        # 禁止在设备代号、里程标记中插入逗号
        if re.search(r'[A-Za-z]+[0-9零一二三四五六七八九十幺]+', segment):
            return False
        if re.search(r'K\s*[0-9零一二三四五六七八九十幺]+', segment):
            return False
        if re.search(r'W\s*[0-9零一二三四五六七八九十幺]+', segment):
            return False

        # 禁止在时间表达式中插入逗号
        if re.search(r'[0-9零一二三四五六七八九十幺]+点[0-9零一二三四五六七八九十幺]*分?', segment):
            return False
        if re.search(r'[0-9零一二三四五六七八九十幺]+年[0-9零一二三四五六七八九十幺]*月[0-9零一二三四五六七八九十幺]*号?', segment):
            return False

        # 如果前后都是短词（<=2），不插逗号
        prev_len = len(text[max(0, idx-2): idx+1])
        next_len = len(text[idx+1: idx+1+2])
        if prev_len <= 2 and next_len <= 2:
            return False

        # 在特定的铁路术语分隔处允许逗号
        railway_terms = ['作业负责人', '驻站联络员', '工地防护员', '调度命令', '施工完毕', '计划核对正确']
        for term in railway_terms:
            if term in segment:
                return True

        # 如果出现常见的连接词/介词前后，允许逗号
        if re.search(r'(因为|所以|但是|不过|而且|另外|因此|由于|啊|呃|嗯)', segment):
            return True

        # 默认：较长上下文且非数字串允许逗号
        return len(segment) >= 8

    def _should_use_comma_enhanced(self, text: str, idx: int) -> bool:
        """
        增强版逗号插入规则，参考预训练模型的标点策略：
        - 更宽松的逗号插入条件，但仍避免在数字串中插入
        - 在关键信息分隔处、语气词后、长句中适当插入逗号
        """
        start = max(0, idx-8)
        end = min(len(text), idx+9)
        segment = text[start:end]

        # 严格禁止在数字串、代号串中插入逗号
        if re.search(r'[0-9零一二三四五六七八九十幺]{3,}', segment):
            return False
        if re.search(r'[A-Za-z]+[0-9零一二三四五六七八九十幺]+', segment):
            return False
        if re.search(r'K\s*[0-9零一二三四五六七八九十幺]+', segment):
            return False
        if re.search(r'W\s*[0-9零一二三四五六七八九十幺]+', segment):
            return False

        # 禁止在时间表达式中插入逗号
        if re.search(r'[0-9零一二三四五六七八九十幺]+点[0-9零一二三四五六七八九十幺]*分?', segment):
            return False
        if re.search(r'[0-9零一二三四五六七八九十幺]+年[0-9零一二三四五六七八九十幺]*月', segment):
            return False

        # 在铁路专业术语分隔处优先插入逗号
        railway_separators = [
            '作业负责人', '驻站联络员', '工地防护员', '调度命令', '施工完毕',
            '计划核对正确', '命令内容', '调度员', '车站值班员', '号码',
            '时间', '限', '准许', '项目部', '封锁地段'
        ]
        for term in railway_separators:
            if term in segment:
                return True

        # 在语气词、连接词后插入逗号（参考预训练模型行为）
        if re.search(r'(啊|呃|嗯|好|对|明白|收到|是的|那个|这个)', segment):
            return True

        # 在较长的定语、状语后插入逗号
        if len(segment) >= 10:
            return True

        # 在人名后插入逗号（如"董朝阳作业项目"）
        common_surnames = ['董', '刘', '黄', '陆', '韦', '谢', '李', '王', '张', '陈', '杨', '赵', '孙', '周', '吴', '郑', '冯', '陶']
        for surname in common_surnames:
            if surname in segment and len(segment) >= 6:
                return True

        return False

    def _should_use_pause_enhanced(self, text: str, idx: int) -> bool:
        """
        增强版顿号插入规则，更严格地控制顿号使用：
        - 严格禁止在数字串、代号串、人名中使用
        - 仅在明确的并列词汇处使用
        """
        if idx + 1 >= len(text):
            return False

        # 扩大检查范围
        around = text[max(0, idx-8): idx+9]

        # 严格禁止在各种数字串中使用顿号
        if re.search(r'[0-9零一二三四五六七八九十幺]{2,}', around):
            return False
        if re.search(r'K\s*[0-9零一二三四五六七八九十幺]+', around):
            return False
        if re.search(r'[A-Za-z][0-9零一二三四五六七八九十幺]{2,}', around):
            return False
        if re.search(r'W\s*[0-9零一二三四五六七八九十幺]+', around):
            return False

        # 禁止在时间表达式中使用
        if re.search(r'[0-9零一二三四五六七八九十幺]+点[0-9零一二三四五六七八九十幺]*', around):
            return False
        if re.search(r'[0-9零一二三四五六七八九十幺]+年[0-9零一二三四五六七八九十幺]*月', around):
            return False

        # 禁止在设备代号、工区名称、人名中使用
        if re.search(r'(线路工区|车间|项目部|集团|负责人|联络员|防护员)', around):
            return False

        # 禁止在人名附近使用
        common_surnames = ['董', '刘', '黄', '陆', '韦', '谢', '李', '王', '张', '陈', '杨', '赵', '孙', '周', '吴', '郑', '冯', '陶']
        for surname in common_surnames:
            if surname in around:
                return False

        # 只在明确的并列短词处使用（如"苹果香蕉橙子"类型）
        next_chunk = text[idx+1: idx+4]
        prev_chunk = text[max(0, idx-2):idx+1]

        # 检查是否为明确的并列词汇模式
        if (re.match(r'^[\u4e00-\u9fa5]{2,3}$', next_chunk) and
            re.match(r'^[\u4e00-\u9fa5]{2,3}$', prev_chunk) and
            not any(word in around for word in ['工区', '车间', '项目', '负责', '联络', '防护'])):
            return True

        return False

    def _final_punct_correction(self, joined: str, original: str) -> str:
        """依据疑问词或语气词调整句末标点（增强版疑问句识别）"""
        # 扩展疑问词列表，特别针对铁路通信场景
        q_triggers = [
            '吗', '么', '呢', '是否', '是不是', '有没有', '请问', '为何', '为什么',
            '到哪里', '在哪', '怎么样', '如何', '多少', '几点', '什么时候',
            '检查完没有', '核对正确', '明白', '收到', '好了没', '完成了吗',
            '可以', '行不行', '对不对', '是吧', '对吧', '搞啥呢', '现在搞啥呢'
        ]

        # 增强疑问句模式匹配，特别关注铁路通信中的疑问表达
        question_patterns = [
            r'.*到哪里了[。]?$',           # "上行到哪里了"
            r'.*检查完没有[。]?$',         # "李队检查完没有"
            r'.*核对正确[。]?$',           # "计划核对正确"
            r'.*明白[。]?$',               # "白回校明白"
            r'.*收到[。]?$',               # "收到"
            r'.*可以.*[。]?$',             # "可以了"
            r'.*怎么.*[。]?$',             # "怎么样"
            r'.*什么.*[。]?$',             # "搞什么"
            r'.*多少.*[。]?$',             # "多少"
            r'.*几.*[。]?$',               # "几点"
            r'.*搞啥呢[。]?$',             # "现在搞啥呢"
            r'.*现在搞啥呢[。]?$',         # "白回校现在搞啥呢"
            r'.*要牌[。]?$',               # "要牌"相关疑问
            r'.*过后[。]?$',               # "下行过后"等
        ]

        # 特殊疑问句识别：基于语境的疑问判断
        context_question_patterns = [
            r'.*白回校.*明白[。]?$',       # 对话确认类
            r'.*快.*要牌.*[。]?$',         # 铁路调度疑问
            r'.*下行.*过后.*[。]?$',       # 状态询问
        ]

        lower_orig = original.strip()

        # 检查疑问词
        if any(trigger in lower_orig for trigger in q_triggers):
            joined = re.sub(r'[。！？]+$', '？', joined)
            return joined

        # 检查疑问句模式
        for pattern in question_patterns:
            if re.match(pattern, lower_orig):
                joined = re.sub(r'[。！？]+$', '？', joined)
                return joined

        # 检查上下文疑问句模式
        for pattern in context_question_patterns:
            if re.match(pattern, lower_orig):
                joined = re.sub(r'[。！？]+$', '？', joined)
                return joined

        # 感叹句识别
        ex_triggers = ['太', '好棒', '真是', '太好了', '太厉害', '我的天', '天啊']
        if any(trigger in lower_orig for trigger in ex_triggers):
            joined = re.sub(r'[。！？]+$', '！', joined)
            return joined

        # 默认：如果没有句末标点则加句号
        if not re.search(r'[。！？]$', joined):
            joined = joined + '。'
        return joined

    def _post_process_punctuation(self, text: str) -> str:
        # 合并重复的句末标点
        text = re.sub(r'([，。！？、])\1+', r'\1', text)
        # 顿号后若误接逗号则清理
        text = text.replace('、，', '、')
        # 删除标点前多余空格
        text = re.sub(r'\s+([，。！？、])', r'\1', text)

        # 增强数字串标点清理规则
        # 清理数字串中的逗号（如"两，九四八" -> "两九四八"）
        text = re.sub(r'([0-9零一二三四五六七八九十幺]),([0-9零一二三四五六七八九十幺])', r'\1\2', text)
        # 清理代号串中的逗号（如"四拐，九道" -> "四拐九道"）
        text = re.sub(r'([0-9零一二三四五六七八九十幺][拐道]),([0-9零一二三四五六七八九十幺][拐道])', r'\1\2', text)
        # 清理里程标记中的逗号（如"K，七" -> "K七"）
        text = re.sub(r'([KW]),([0-9零一二三四五六七八九十幺])', r'\1\2', text)
        # 清理时间表达式中的逗号
        text = re.sub(r'([0-9零一二三四五六七八九十幺]+点),([0-9零一二三四五六七八九十幺]+)', r'\1\2', text)

        # 去除句中极短处的错误逗号（如 单字,单字 -> 单字单字）
        text = re.sub(r'([\u4e00-\u9fa5]),([\u4e00-\u9fa5])', r'\1\2', text)
        # 清理明显不合理的“单字、单字”样式顿号
        text = re.sub(r'([\u4e00-\u9fa5])、([\u4e00-\u9fa5])', r'\1\2', text)
        # 禁止在连续数字/时间/里程标记中出现顿号
        text = re.sub(r'([0-9零一二三四五六七八九十幺]{1,})、([0-9零一二三四五六七八九十幺]{1,})', r'\1\2', text)
        # 清理设备代号中的顿号
        text = re.sub(r'([A-Za-z]+[0-9零一二三四五六七八九十幺]+)、([A-Za-z]*[0-9零一二三四五六七八九十幺]+)', r'\1\2', text)
        # 密度控制：滑动窗口内最多一个标点，且标点之间至少间隔3个字符
        cleaned = []
        last_punc_pos = -100
        window = 8  # 由 6 调整为 8
        for i, ch in enumerate(text):
            if ch in '，。？！、':
                if i - last_punc_pos < 3:
                    continue
                # 在窗口内已存在标点则跳过
                start = max(0, i - window + 1)
                if any(c in '，。？！、' for c in text[start:i]):
                    continue
                last_punc_pos = i
                cleaned.append(ch)
            else:
                cleaned.append(ch)
        text = ''.join(cleaned)
        # 最终保证末尾有标点
        if not re.search(r'[。！？]$', text):
            text = text + '。'
        return text
    def _railway_post_process(self, text: str) -> str:
        """铁路专用后处理（更保守）：减少对口语填充词的强制逗号插入"""
        text = re.sub(r'(\d{4}年\d{1,2}月\d{1,2}号?)(?!\,)', r'\1，', text)
        text = re.sub(r'(零点\d{1,2}分?|零点\d{1,2}到\d{1,2}点\d{1,2})(?!\,)', r'\1，', text)
        text = re.sub(r'(施工负责人|驻站联络员|工地防护员|负责人|驻站)(\w{2,4})(?!\,)', r'\1，\2', text)
        text = re.sub(r'(上道号|天窗命令号|作业里程|计划号)(\w{4,})(?!\,)', r'\1，\2，', text)
        text = re.sub(r'(是否正确|是否核对正确)(？|。|\.|！)?$', r'\1？', text)
        # 仅当前后都不是标点时才在口语填充词后加逗号，避免过密
        text = re.sub(r'(?<![，、。？！])(呃|啊|嗯)(?![，、。？！])', r'\1，', text)
        return text

    def _viterbi_smooth_labels(self, text: str, probs: torch.Tensor, attention_mask: torch.Tensor = None):
        """
        使用简化版 Viterbi 进行序列级平滑，抑制过度标点：
        - 发射概率：softmax 概率的对数 + 类别偏置（对非 O 标签施加惩罚，句末标点更强）
        - 转移惩罚：惩罚连续标点与标点->标点的跃迁，鼓励回到 O
        - 句末标点（。？！）在非边界位置附加发射惩罚
        返回：长度为 seq 的最佳标签 ID 序列（与 probs 的 seq 对齐，CLS 位置强制为 O）
        """
        # 形状与准备
        seq_len, num_labels = probs.shape
        labels = self.config.LABELS
        label_to_id = self.config.LABEL_TO_ID
        O = label_to_id.get('O', 0)
        end_punc_labels = ['PERIOD', 'QUESTION', 'EXCLAIM']
        end_ids = set([label_to_id[l] for l in end_punc_labels if l in label_to_id])
        punc_ids = set([i for i, l in enumerate(labels) if l != 'O'])

        # 构建发射对数概率 + 类别偏置
        eps = 1e-8
        emit = torch.log(probs.clamp(min=eps))  # [seq, num_labels]
        label_bias = torch.zeros(num_labels, dtype=emit.dtype)
        for i, l in enumerate(labels):
            if l != 'O':
                # 类别偏置：逗号/顿号更温和，句末标点更强
                if i in end_ids:
                    label_bias[i] = -1.0  # PERIOD/QUESTION/EXCLAIM 保持 -1.0
                else:
                    label_bias[i] = -0.6  # COMMA/PAUSE 从 -0.8 调至 -0.6
        emit = emit + label_bias.unsqueeze(0)

        # 将 CLS 位置（t=0）强制为 O（其它标签 -inf）
        neg_inf = -1e9
        if seq_len > 0:
            mask_row = torch.full((num_labels,), neg_inf, dtype=emit.dtype)
            mask_row[O] = 0.0
            emit[0] = emit[0] + mask_row

        # attention_mask 为 0 的位置强制为 O（PAD/SEP 等）
        if attention_mask is not None:
            for t in range(seq_len):
                try:
                    if int(attention_mask[t].item()) == 0:
                        mask_row = torch.full((num_labels,), neg_inf, dtype=emit.dtype)
                        mask_row[O] = 0.0
                        emit[t] = emit[t] + mask_row
                except Exception:
                    pass

        # 根据文本边界，加大非边界位置的句末标点惩罚
        pos2char = [None] * seq_len
        char_idx = -1
        for t in range(seq_len):
            if t == 0:
                pos2char[t] = None
                continue
            if (attention_mask is None) or (int(attention_mask[t].item()) == 1):
                char_idx += 1
                pos2char[t] = char_idx
        for t in range(seq_len):
            c = pos2char[t]
            if c is None:
                continue
            is_end = (c == len(text) - 1) or self._is_sentence_boundary(text, c)
            if not is_end:
                for eid in end_ids:
                    emit[t, eid] = emit[t, eid] - 2.0

        # 构建转移对数概率矩阵 trans[x,y]
        trans = torch.zeros((num_labels, num_labels), dtype=emit.dtype)
        for x in range(num_labels):
            for y in range(num_labels):
                pen = 0.0
                if x == O and y in punc_ids:
                    pen -= 0.6
                if x in punc_ids and y in punc_ids:
                    pen -= 1.8  # 调整：连续标点更强惩罚
                if x in end_ids and y != O:
                    pen -= 1.0
                trans[x, y] = pen

        # Viterbi DP
        dp = torch.full((seq_len, num_labels), neg_inf, dtype=emit.dtype)
        bp = torch.full((seq_len, num_labels), -1, dtype=torch.long)
        dp[0] = emit[0]
        for t in range(1, seq_len):
            prev = dp[t-1].unsqueeze(1) + trans  # [num_labels, num_labels]
            best_prev_vals, best_prev_idx = torch.max(prev, dim=0)
            dp[t] = emit[t] + best_prev_vals
            bp[t] = best_prev_idx

        # 回溯
        path = [0] * seq_len
        last_label = int(torch.argmax(dp[-1]).item())
        path[-1] = last_label
        for t in range(seq_len-1, 0, -1):
            last_label = int(bp[t, last_label].item())
            path[t-1] = last_label
        return path


    def predict_batch(self, texts: list) -> list:
        return [self.predict(t) for t in texts]

    def save_prediction_results(self, results: list, output_file: str = "prediction_results.json", console_output: str = ""):
        """
        保存预测结果到JSON文件，包含完整的控制台输出

        Args:
            results: 预测结果列表，每个元素包含原文、预训练模型结果、微调模型结果等
            output_file: 输出文件名
            console_output: 完整的控制台输出内容（原始格式）
        """
        try:
            # 添加时间戳和元数据
            output_data = {
                "timestamp": datetime.datetime.now().isoformat(),
                "total_samples": len(results),
                "metadata": {
                    "model_type": "CT-Transformer",
                    "has_finetuned_model": self.use_finetuned,
                    "device": str(self.device)
                },
                "console_output": console_output,  # 保存完整的原始控制台输出
                "results": results
            }

            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(output_data, f, ensure_ascii=False, indent=2)

            logger.info(f"✅ 预测结果已保存到: {output_file}")
            print(f"📄 预测结果已保存到: {output_file}")

        except Exception as e:
            logger.error(f"保存预测结果失败: {e}")
            print(f"❌ 保存预测结果失败: {e}")

    def compare_models_with_debug(self, texts: list) -> list:
        """
        对比两个模型的预测结果并保存调试信息

        Args:
            texts: 待预测的文本列表

        Returns:
            包含对比结果的列表
        """
        results = []
        console_output_lines = []  # 收集所有控制台输出

        for i, text in enumerate(texts):
            sample_output = f"📄 测试样例 {i+1}: {text}"
            print(sample_output)
            console_output_lines.append(sample_output)

            separator = "-" * 80
            print(separator)
            console_output_lines.append(separator)

            # 预训练模型预测
            pretrained_result = ""
            if self.am is not None:
                try:
                    pretrained_result = self._predict_with_pretrained_model(text)
                except Exception as e:
                    logger.error(f"预训练模型预测失败: {e}")
                    pretrained_result = text

            # 微调模型预测
            finetuned_result = ""
            if self.use_finetuned and self.model is not None:
                try:
                    finetuned_result = self._predict_with_finetuned_model(text)
                except Exception as e:
                    logger.error(f"微调模型预测失败: {e}")
                    finetuned_result = text

            # 输出对比结果
            pretrained_output = f"🔧 预训练模型: {pretrained_result}"
            finetuned_output = f"🎯 微调模型: {finetuned_result}"
            print(pretrained_output)
            print(finetuned_output)
            console_output_lines.append(pretrained_output)
            console_output_lines.append(finetuned_output)

            # 检查差异
            has_difference = pretrained_result != finetuned_result
            if has_difference:
                diff_msg = "✨ 模型结果存在差异！"
                print(diff_msg)
                console_output_lines.append(diff_msg)
            else:
                same_msg = "✅ 模型结果一致"
                print(same_msg)
                console_output_lines.append(same_msg)
            print()
            console_output_lines.append("")

            # 保存结果
            result_item = {
                "sample_id": i + 1,
                "original_text": text,
                "pretrained_result": pretrained_result,
                "finetuned_result": finetuned_result,
                "has_difference": has_difference,
                "difference_analysis": self._analyze_difference(pretrained_result, finetuned_result) if has_difference else None
            }
            results.append(result_item)

        # 将控制台输出合并为字符串
        full_console_output = "\n".join(console_output_lines)

        # 保存调试结果，包含完整的控制台输出
        self.save_prediction_results(results, console_output=full_console_output)

        return results

    def _analyze_difference(self, pretrained: str, finetuned: str) -> dict:
        """
        分析两个模型预测结果的差异

        Args:
            pretrained: 预训练模型结果
            finetuned: 微调模型结果

        Returns:
            差异分析结果
        """
        analysis = {
            "length_diff": len(finetuned) - len(pretrained),
            "punctuation_count": {
                "pretrained": {
                    "comma": pretrained.count('，'),
                    "period": pretrained.count('。'),
                    "question": pretrained.count('？'),
                    "exclamation": pretrained.count('！'),
                    "pause": pretrained.count('、')
                },
                "finetuned": {
                    "comma": finetuned.count('，'),
                    "period": finetuned.count('。'),
                    "question": finetuned.count('？'),
                    "exclamation": finetuned.count('！'),
                    "pause": finetuned.count('、')
                }
            },
            "issues_detected": []
        }

        # 检测常见问题
        if '，' in finetuned and re.search(r'[0-9零一二三四五六七八九十幺],', finetuned):
            analysis["issues_detected"].append("数字串中存在逗号")

        if finetuned.endswith('。') and pretrained.endswith('？'):
            analysis["issues_detected"].append("疑问句标点错误")

        if analysis["punctuation_count"]["finetuned"]["comma"] < analysis["punctuation_count"]["pretrained"]["comma"]:
            analysis["issues_detected"].append("微调模型逗号偏少")

        return analysis


class PunctuationComparisonPredictor:
    """用于对比预训练与微调模型"""
    def __init__(self):
        self.config = PunctuationConfig()
        # 准备两个预测器：预训练 与 微调（若存在）
        try:
            # 预训练强制加载
            pretrained = PunctuationPredictor()
            try:
                pretrained._load_pretrained_model()
            except Exception:
                pass
            self.pretrained_predictor = pretrained
            if self.config.has_finetuned_model():
                self.finetuned_predictor = PunctuationPredictor()
            else:
                self.finetuned_predictor = None
        except Exception as e:
            logger.error(f"预测器初始化失败: {e}")
            self.pretrained_predictor = None
            self.finetuned_predictor = None

    def compare_predictions(self, texts: list) -> list:
        results = []
        for text in texts:
            r = {'original': text, 'pretrained': None, 'finetuned': None, 'is_different': False}
            if self.pretrained_predictor:
                try:
                    r['pretrained'] = self.pretrained_predictor.predict(text)
                except Exception as e:
                    r['pretrained'] = f"预训练预测失败: {e}"
            if self.finetuned_predictor:
                try:
                    r['finetuned'] = self.finetuned_predictor.predict(text)
                except Exception as e:
                    r['finetuned'] = f"微调预测失败: {e}"
            r['is_different'] = (r['pretrained'] != r.get('finetuned'))
            results.append(r)
        return results

    def compare_models_with_debug(self, texts: list) -> list:
        """
        对比两个模型的预测结果并保存调试信息
        为PunctuationComparisonPredictor类实现此方法
        """
        results = []
        console_output_lines = []  # 收集所有控制台输出

        for i, text in enumerate(texts):
            sample_output = f"📄 测试样例 {i+1}: {text}"
            print(sample_output)
            console_output_lines.append(sample_output)

            separator = "-" * 80
            print(separator)
            console_output_lines.append(separator)

            # 预训练模型预测
            pretrained_result = ""
            if self.pretrained_predictor:
                try:
                    pretrained_result = self.pretrained_predictor.predict(text)
                except Exception as e:
                    logger.error(f"预训练模型预测失败: {e}")
                    pretrained_result = f"预训练预测失败: {e}"

            # 微调模型预测
            finetuned_result = ""
            if self.finetuned_predictor:
                try:
                    finetuned_result = self.finetuned_predictor.predict(text)
                except Exception as e:
                    logger.error(f"微调模型预测失败: {e}")
                    finetuned_result = f"微调预测失败: {e}"

            # 输出对比结果
            pretrained_output = f"🔧 预训练模型: {pretrained_result}"
            finetuned_output = f"🎯 微调模型: {finetuned_result}"
            print(pretrained_output)
            print(finetuned_output)
            console_output_lines.append(pretrained_output)
            console_output_lines.append(finetuned_output)

            # 检查差异
            has_difference = pretrained_result != finetuned_result
            if has_difference:
                diff_msg = "✨ 模型结果存在差异！"
                print(diff_msg)
                console_output_lines.append(diff_msg)
            else:
                same_msg = "✅ 模型结果一致"
                print(same_msg)
                console_output_lines.append(same_msg)
            print()
            console_output_lines.append("")

            # 保存结果
            result_item = {
                "sample_id": i + 1,
                "original_text": text,
                "pretrained_result": pretrained_result,
                "finetuned_result": finetuned_result,
                "has_difference": has_difference,
                "difference_analysis": self._analyze_difference(pretrained_result, finetuned_result) if has_difference else None
            }
            results.append(result_item)

        # 将控制台输出合并为字符串
        full_console_output = "\n".join(console_output_lines)

        # 保存调试结果，包含完整的控制台输出
        self.save_prediction_results(results, console_output=full_console_output)

        return results

    def save_prediction_results(self, results: list, output_file: str = "prediction_results.json", console_output: str = ""):
        """
        保存预测结果到JSON文件，包含完整的控制台输出
        为PunctuationComparisonPredictor类实现此方法
        """
        try:
            # 添加时间戳和元数据
            output_data = {
                "timestamp": datetime.datetime.now().isoformat(),
                "total_samples": len(results),
                "metadata": {
                    "model_type": "CT-Transformer-Comparison",
                    "has_pretrained_model": self.pretrained_predictor is not None,
                    "has_finetuned_model": self.finetuned_predictor is not None,
                },
                "console_output": console_output,  # 保存完整的原始控制台输出
                "results": results
            }

            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(output_data, f, ensure_ascii=False, indent=2)

            logger.info(f"✅ 预测结果已保存到: {output_file}")
            print(f"📄 预测结果已保存到: {output_file}")

        except Exception as e:
            logger.error(f"保存预测结果失败: {e}")
            print(f"❌ 保存预测结果失败: {e}")

    def _analyze_difference(self, pretrained: str, finetuned: str) -> dict:
        """
        分析两个模型预测结果的差异
        为PunctuationComparisonPredictor类实现此方法
        """
        analysis = {
            "pretrained_length": len(pretrained) if pretrained else 0,
            "finetuned_length": len(finetuned) if finetuned else 0,
            "length_difference": 0,
            "punctuation_difference": {},
            "issues_detected": []
        }

        if not pretrained or not finetuned:
            analysis["issues_detected"].append("其中一个模型预测失败")
            return analysis

        analysis["length_difference"] = len(finetuned) - len(pretrained)

        # 统计标点符号差异
        punctuations = ['，', '。', '？', '！', '、']
        for punct in punctuations:
            pretrained_count = pretrained.count(punct)
            finetuned_count = finetuned.count(punct)
            if pretrained_count != finetuned_count:
                analysis["punctuation_difference"][punct] = {
                    "pretrained": pretrained_count,
                    "finetuned": finetuned_count,
                    "difference": finetuned_count - pretrained_count
                }

        # 检测常见问题
        if analysis["length_difference"] > 10:
            analysis["issues_detected"].append("微调模型标点过多")
        elif analysis["length_difference"] < -5:
            analysis["issues_detected"].append("微调模型标点过少")

        return analysis

    def print_comparison(self, texts: list):
        # 使用 compare_models_with_debug 方法，确保保存 JSON 文件
        results = self.compare_models_with_debug(texts)

        # 打印汇总信息
        print("\n" + "="*80)
        total = len(results)
        diff = sum(1 for r in results if r['has_difference'])
        print(f"📊 总计: {total} 条, 差异: {diff} 条 ({diff/total*100:.1f}%)")
        print("="*80)
        return results


def main():
    print("🔮 中文标点符号预测演示")
    print("="*50)
    try:
        choice = input("请选择模式 (1: 单模型预测, 2: 模型对比): ").strip()
        if choice == '1':
            try:
                import funasr
            except ImportError:
                print("❌ 缺少 funasr，请先安装: pip install funasr")
                return
            predictor = PunctuationPredictor()
            model_type = "微调模型" if predictor.use_finetuned else "预训练模型"
            print(f"🤖 当前使用: {model_type}")
            config = PunctuationConfig()
            valid_file = config.VALID_FILE
            test_sentences = []
            if os.path.exists(valid_file):
                with open(valid_file, 'r', encoding='utf-8') as f:
                    cur = []
                    for line in f:
                        line = line.strip()
                        if line == "":
                            if cur:
                                test_sentences.append(''.join([c.split('\t')[0] for c in cur]))
                                cur = []
                        else:
                            cur.append(line)
                    if cur:
                        test_sentences.append(''.join([c.split('\t')[0] for c in cur]))
            else:
                test_sentences = ["苹果香蕉橙子都很新鲜", "今天天气很好我们去公园玩吧", "你觉得这个项目怎么样"]
            for i, text in enumerate(test_sentences, 1):
                print(f"{i}. 原文: {text}")
                print(f"   结果: {predictor.predict(text)}\n")
            print("💬 交互式预测（输入 'quit' 退出）:")
            while True:
                user_input = input("\n请输入文本: ").strip()
                if user_input.lower() in ['quit', 'exit', '退出']:
                    break
                if user_input:
                    print(f"预测结果: {predictor.predict(user_input)}")
        elif choice == '2':
            try:
                import funasr
            except ImportError:
                print("❌ 缺少 funasr，请先安装: pip install funasr")
                return
            comparator = PunctuationComparisonPredictor()
            test_sentences = [
                "郑勇叶为福文丰博防护的点外作业组都作业完毕了全部人员工机具都安全撤离线路撤出网外下好道不用联络下道时间三点零幺分",
                "二零二五年七月十五号白云线路工区作业项目捣固作业地点广州白云京广场站内道岔作业时段零点幺零至三点幺零",
                "苹果香蕉橙子葡萄都很新鲜"
            ]
            comparator.print_comparison(test_sentences)
            print("\n💬 交互式对比（输入 'back' 返回）：")
            while True:
                user_input = input("\n请输入文本: ").strip()
                if user_input.lower() in ['back', '返回', 'quit', 'exit']:
                    break
                if user_input:
                    comparator.print_comparison([user_input])
        else:
            print("❌ 无效选择，请输入 1 或 2")
    except KeyboardInterrupt:
        print("\n\n👋 用户中断，再见！")
    except Exception as e:
        logger.error(f"程序出错: {e}")
        print(f"❌ 程序出错: {e}")

if __name__ == "__main__":
    main()
