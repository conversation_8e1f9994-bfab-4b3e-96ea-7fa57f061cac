# -*- coding: utf-8 -*-
"""
训练模块 - 已修正标签映射并做输入检查
"""
import os
import sys
import time
import logging
import json
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.optim import AdamW
from torch.utils.data import DataLoader, Dataset
from transformers import get_linear_schedule_with_warmup
from sklearn.metrics import accuracy_score, precision_recall_fscore_support
from tqdm import tqdm
import pickle
import hashlib
from config import PunctuationConfig
from improved_data_processor import ImprovedDataProcessor

# 统一而安全的日志配置，与 inference.py 保持一致，增强容错
logging.basicConfig(level=logging.INFO)
logging.raiseExceptions = False
logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)
logger.propagate = False
if not logger.handlers:
    _sh = logging.StreamHandler(stream=sys.stdout)
    _sh.setLevel(logging.INFO)
    _sh.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(name)s - %(message)s'))
    logger.addHandler(_sh)


def _ensure_logging_handlers_open():
    """
    修复可能存在的已关闭日志处理器，避免 'I/O operation on closed file'。
    仅处理根日志器和常见的外部库日志器，不改变其它逻辑。
    """
    try:
        candidate_names = ["", "funasr", "modelscope", "huggingface_hub"]
        for name in candidate_names:
            lg = logging.getLogger(name) if name else logging.getLogger()
            handlers = list(getattr(lg, "handlers", []))
            changed = False
            for h in handlers:
                stream = getattr(h, "stream", None)
                is_closed = (stream is None) or (hasattr(stream, "closed") and getattr(stream, "closed", False))
                if is_closed:
                    try:
                        lg.removeHandler(h)
                        changed = True
                    except Exception:
                        pass
            if (not lg.handlers) and (name == ""):
                try:
                    sh = logging.StreamHandler(stream=sys.stderr)
                    sh.setLevel(logging.INFO)
                    sh.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(name)s - %(message)s'))
                    lg.addHandler(sh)
                    lg.setLevel(logging.INFO)
                except Exception:
                    pass
            if changed:
                try:
                    lg.disabled = False
                    if name:
                        lg.propagate = True
                except Exception:
                    pass
    except Exception:
        # 兜底：不让日志修复影响训练主流程
        pass

class CTFeaturePrecomputer:
    """CT-Transformer特征预计算器，避免训练时重复推理"""

    def __init__(self, ct_model, cache_dir="./output/ct_features_cache", feature_fn=None):
        self.ct_model = ct_model
        self.cache_dir = cache_dir
        self.feature_fn = feature_fn  # 可选的特征提取回调：text -> torch.Tensor[seq, hidden]
        self.device = torch.device('cpu')
        os.makedirs(cache_dir, exist_ok=True)
        logger.info(f"✅ CT特征预计算器初始化完成，缓存目录: {cache_dir}")

    def _get_cache_key(self, text):
        """生成文本的缓存键"""
        return hashlib.md5(text.encode('utf-8')).hexdigest()

    def _get_cache_path(self, cache_key):
        """获取缓存文件路径"""
        return os.path.join(self.cache_dir, f"{cache_key}.pkl")

    def precompute_features(self, texts, batch_size=8):
        """批量预计算CT特征"""
        logger.info(f"🔄 开始预计算 {len(texts)} 个文本的CT特征...")

        features_dict = {}
        cache_hits = 0
        cache_misses = 0

        # 检查缓存
        for text in texts:
            cache_key = self._get_cache_key(text)
            cache_path = self._get_cache_path(cache_key)

            if os.path.exists(cache_path):
                try:
                    with open(cache_path, 'rb') as f:
                        features_dict[text] = pickle.load(f)
                    cache_hits += 1
                except:
                    cache_misses += 1
            else:
                cache_misses += 1

        logger.info(f"📊 缓存命中: {cache_hits}, 缓存未命中: {cache_misses}")

        # 计算未缓存的特征
        uncached_texts = [text for text in texts if text not in features_dict]

        if uncached_texts:
            logger.info(f"🔄 计算 {len(uncached_texts)} 个未缓存文本的特征...")

            for i in range(0, len(uncached_texts), batch_size):
                batch_texts = uncached_texts[i:i+batch_size]
                batch_features = self._compute_batch_features(batch_texts)

                # 保存到缓存和结果字典
                for text, feature in zip(batch_texts, batch_features):
                    features_dict[text] = feature
                    self._save_to_cache(text, feature)

                if (i // batch_size + 1) % 10 == 0:
                    logger.info(f"已处理 {min(i + batch_size, len(uncached_texts))}/{len(uncached_texts)} 个文本")

        logger.info("✅ CT特征预计算完成")
        return features_dict

    def _compute_batch_features(self, texts):
        """计算一批文本的CT特征"""
        features = []

        for text in texts:
            try:
                # 使用CT-Transformer提取特征
                with torch.no_grad():
                    # 抑制输出
                    import os
                    from contextlib import redirect_stdout, redirect_stderr

                    with open(os.devnull, 'w') as devnull:
                        with redirect_stdout(devnull), redirect_stderr(devnull):
                            # 调用CT-Transformer（仅为构建特征触发一次，无需使用结果）
                            _ = self.ct_model.generate(input=text)

                    # 创建基于文本的特征表示（简化版本）
                    # 这里可以根据实际的CT-Transformer输出格式进行调整
                    text_len = min(len(text), 510)  # 保留CLS和SEP位置
                    feature = torch.zeros(512, 768, dtype=torch.float32)  # 标准的hidden_size

                    # 基于字符编码创建特征
                    for i, char in enumerate(text[:text_len]):
                        char_code = ord(char)
                        # 创建字符特征向量
                        char_feature = torch.randn(768) * 0.1
                        char_feature[0] = char_code / 10000.0  # 归一化字符编码
                        feature[i+1] = char_feature  # 跳过CLS位置

                    features.append(feature)

            except Exception as e:
                logger.warning(f"计算文本特征失败: {e}")
                # 回退到随机特征
                features.append(torch.randn(512, 768, dtype=torch.float32))

        return features

    def _save_to_cache(self, text, feature):
        """保存特征到缓存"""
        try:
            cache_key = self._get_cache_key(text)
            cache_path = self._get_cache_path(cache_key)
            with open(cache_path, 'wb') as f:
                pickle.dump(feature, f)
        except Exception as e:
            logger.warning(f"保存缓存失败: {e}")

    def get_feature(self, text):
        """获取单个文本的特征"""
        cache_key = self._get_cache_key(text)
        cache_path = self._get_cache_path(cache_key)

        if os.path.exists(cache_path):
            try:
                with open(cache_path, 'rb') as f:
                    return pickle.load(f)
            except:
                pass

        # 如果缓存不存在，实时计算
        return self._compute_batch_features([text])[0]

class FocalLoss(nn.Module):
    """Focal Loss用于处理类别不平衡问题"""

    def __init__(self, alpha=0.25, gamma=2.0, ignore_index=-100):
        super(FocalLoss, self).__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.ignore_index = ignore_index

    def forward(self, inputs, targets):
        # 计算交叉熵损失
        ce_loss = F.cross_entropy(inputs, targets, ignore_index=self.ignore_index, reduction='none')

        # 计算概率
        pt = torch.exp(-ce_loss)

        # 计算Focal Loss
        focal_loss = self.alpha * (1 - pt) ** self.gamma * ce_loss

        return focal_loss.mean()

class PunctuationDataset(Dataset):
    """标点符号预测数据集"""

    def __init__(self, texts, labels, tokenizer, max_length=512):
        self.texts = texts
        self.labels = labels
        self.tokenizer = tokenizer
        self.max_length = max_length
        self.config = PunctuationConfig()

    def __len__(self):
        return len(self.texts)

    def __getitem__(self, idx):
        text = self.texts[idx]
        labels = self.labels[idx]

        encoding = self.tokenizer(
            text,
            truncation=True,
            padding='max_length',
            max_length=self.max_length,
            return_tensors='pt'
        )

        # 改进的标签对齐逻辑
        label_ids = self._align_labels_with_tokens(text, labels, encoding)

        return {
            'input_ids': encoding['input_ids'].flatten(),
            'attention_mask': encoding['attention_mask'].flatten(),
            'labels': torch.tensor(label_ids, dtype=torch.long),
            'text': text
        }

    def _align_labels_with_tokens(self, text, labels, encoding):
        """改进的标签对齐方法"""
        label_ids = [-100] * self.max_length

        # 获取tokenizer的tokens
        tokens = self.tokenizer.tokenize(text)

        # 确保标签数量与文本字符数量一致
        if len(labels) != len(text):
            logger.warning(f"标签数量({len(labels)})与文本长度({len(text)})不匹配")
            # 调整标签长度
            if len(labels) < len(text):
                labels = labels + ['O'] * (len(text) - len(labels))
            else:
                labels = labels[:len(text)]

        # CLS token
        label_ids[0] = self.config.LABEL_TO_ID['O']

        # 字符级到token级的映射
        char_idx = 0
        token_idx = 1  # 跳过CLS

        for _ in tokens:
            if token_idx >= self.max_length - 1:  # 保留SEP位置
                break

            if char_idx < len(text) and char_idx < len(labels):
                # 使用字符对应的标签
                label = labels[char_idx]
                label_ids[token_idx] = self.config.LABEL_TO_ID.get(label, self.config.LABEL_TO_ID['O'])
                char_idx += 1
            else:
                label_ids[token_idx] = self.config.LABEL_TO_ID['O']

            token_idx += 1

        # SEP token
        if token_idx < self.max_length:
            label_ids[token_idx] = self.config.LABEL_TO_ID['O']

        return label_ids

class PunctuationTrainer:
    """标点符号预测模型训练器"""

    def __init__(self):
        # 在训练器初始化之初，修复日志处理器以防外部库加载过程写入已关闭流
        _ensure_logging_handlers_open()

        self.config = PunctuationConfig()
        self.device = self._setup_device()

        # 初始化特征预计算器和损失函数
        self.feature_precomputer = None
        self.focal_loss = None
        if hasattr(self.config, 'USE_FOCAL_LOSS') and self.config.USE_FOCAL_LOSS:
            self.focal_loss = FocalLoss(
                alpha=getattr(self.config, 'FOCAL_ALPHA', 0.25),
                gamma=getattr(self.config, 'FOCAL_GAMMA', 2.0)
            )
            logger.info("✅ 启用Focal Loss处理类别不平衡")

        logger.info("正在加载CT-Transformer模型用于微调...")

        # 在调用外部库前再次确保日志处理器健康
        _ensure_logging_handlers_open()

        try:
            from funasr import AutoModel
            import warnings
            from contextlib import redirect_stdout, redirect_stderr

            local_cache_dir = os.path.join(os.getcwd(), "models", "pretrained")
            os.makedirs(local_cache_dir, exist_ok=True)

            # 完全抑制FunASR的所有输出
            print("🔄 正在加载CT-Transformer预训练模型...")

            # 设置环境变量抑制各种输出
            os.environ['TQDM_DISABLE'] = '1'
            os.environ['TRANSFORMERS_VERBOSITY'] = 'error'

            # 抑制警告
            warnings.filterwarnings('ignore')

            # 使用上下文管理器重定向输出
            with open(os.devnull, 'w') as devnull:
                with redirect_stdout(devnull), redirect_stderr(devnull):
                    # 加载CT-Transformer的预训练模型和tokenizer
                    self.ct_model = AutoModel(
                        model="damo/punc_ct-transformer_zh-cn-common-vocab272727-pytorch",
                        model_revision="v2.0.4",
                        disable_update=True,
                        device=self.device.type,
                        cache_dir=local_cache_dir
                    )

            # 清理环境变量
            if 'TQDM_DISABLE' in os.environ:
                del os.environ['TQDM_DISABLE']
            if 'TRANSFORMERS_VERBOSITY' in os.environ:
                del os.environ['TRANSFORMERS_VERBOSITY']

            print("✅ CT-Transformer模型加载完成")
            # 在训练开始前，预先打印一次可能的编码器模块，避免在进度条期间输出INFO干扰
            try:
                self._log_ct_candidates_once()
            except Exception:
                pass

            # 尝试获取CT-Transformer的内部tokenizer
            try:
                # 从CT-Transformer模型中提取tokenizer
                self.tokenizer = self._extract_ct_tokenizer()
                logger.info("✅ 成功提取CT-Transformer tokenizer")
            except Exception as e:
                logger.warning(f"提取CT-Transformer tokenizer失败，使用兼容tokenizer: {e}")
                self.tokenizer = self._create_compatible_tokenizer()

            # 创建基于CT-Transformer的分类模型
            self.model = self._create_ct_based_model()

            try:
                self.model = self.model.to(self.device)
                logger.info(f"✅ 模型成功移动到设备: {self.device}")
            except RuntimeError as e:
                if "CUDA" in str(e):
                    logger.warning(f"CUDA设备移动失败，回退到CPU: {e}")
                    self.device = torch.device('cpu')
                    self.model = self.model.to(self.device)
                else:
                    raise

            logger.info("✅ CT-Transformer微调模型初始化成功")
            # 若探测到了候选编码器模块，这里只在进度条开始前输出一次INFO，训练过程中不再打印
            if hasattr(self, '_ct_encoder_modules_found') and self._ct_encoder_modules_found:
                logger.info(f"CT模型内可能的编码器模块: {self._ct_encoder_modules_found}（需要确认其前向签名后接入）")

            # 初始化特征预计算器（保持向后兼容）
            if hasattr(self.config, 'PRECOMPUTE_CT_FEATURES') and self.config.PRECOMPUTE_CT_FEATURES:
                self.feature_precomputer = CTFeaturePrecomputer(self.ct_model)
                logger.info("✅ CT特征预计算器初始化完成")

        except ImportError:
            logger.error("❌ 缺少 funasr 依赖，请安装: pip install funasr")
            raise
        except Exception as e:
            logger.error(f"❌ 模型初始化失败: {e}")
            raise

        print(f"🚀 CT-Transformer微调模型初始化完成，使用设备: {self.device}")
        print(f"📊 标签数量: {len(self.config.LABELS)}")
        print(f"🔤 标签列表: {self.config.LABELS}")
        if self.focal_loss:
            print(f"🎯 启用Focal Loss，alpha={self.focal_loss.alpha}, gamma={self.focal_loss.gamma}")
        if self.feature_precomputer:
            print(f"⚡ 启用CT特征预计算，缓存目录: {self.feature_precomputer.cache_dir}")

    def _setup_device(self):
        if torch.cuda.is_available():
            try:
                test_tensor = torch.tensor([1.0]).cuda()
                _ = test_tensor + 1
                device = torch.device('cuda')
                logger.info(f"✅ CUDA可用，使用GPU: {torch.cuda.get_device_name()}")
                return device
            except Exception as e:
                logger.warning(f"CUDA测试失败，回退到CPU: {e}")
                return torch.device('cpu')
        else:
            logger.info("CUDA不可用，使用CPU")
            return torch.device('cpu')

    def _extract_ct_tokenizer(self):
        """尝试从CT-Transformer模型中提取tokenizer"""
        try:
            logger.info("🔍 尝试从CT-Transformer提取tokenizer...")

            # 基于调试结果，我们知道CT-Transformer的结构
            # 重点检查 model.punc_list 等属性

            # 方法1: 检查model.punc_list (最有希望的候选)
            if hasattr(self.ct_model, 'model') and hasattr(self.ct_model.model, 'punc_list'):
                punc_list = getattr(self.ct_model.model, 'punc_list')
                if punc_list is not None and hasattr(punc_list, '__len__') and len(punc_list) > 0:
                    logger.info(f"✅ 成功提取CT-Transformer的punc_list: {punc_list}")
                    # 直接创建基于punc_list的tokenizer
                    return self._create_ct_tokenizer_from_punc_list(punc_list)

            # 方法2: 检查其他可能的词汇表属性
            vocab_candidates = []

            # 检查model对象的其他属性
            if hasattr(self.ct_model, 'model'):
                model_obj = self.ct_model.model
                for attr_name in ['vocab', 'token_list', 'char_list', 'vocabulary']:
                    if hasattr(model_obj, attr_name):
                        attr_value = getattr(model_obj, attr_name)
                        if attr_value is not None:
                            vocab_candidates.append((f"model.{attr_name}", attr_value))

            # 检查主对象的属性
            for attr_name in ['vocab', 'token_list', 'char_list', 'vocabulary']:
                if hasattr(self.ct_model, attr_name):
                    attr_value = getattr(self.ct_model, attr_name)
                    if attr_value is not None:
                        vocab_candidates.append((f"主对象.{attr_name}", attr_value))

            # 验证候选词汇表
            for source, vocab in vocab_candidates:
                if self._validate_vocab(vocab):
                    logger.info(f"✅ 找到有效词汇表: {source}")
                    return self._create_ct_tokenizer_from_vocab(vocab)

            # 如果都没找到，抛出异常
            raise AttributeError("无法从CT-Transformer中提取有效的tokenizer或词汇表")

        except Exception as e:
            logger.warning(f"提取CT-Transformer tokenizer失败: {e}")
            raise

    def _validate_vocab(self, vocab):
        """验证词汇表是否有效"""
        try:
            # 检查是否是列表或元组，且有足够的词汇
            if isinstance(vocab, (list, tuple)) and len(vocab) > 100:
                return True

            # 检查是否是字典，且有足够的词汇
            if isinstance(vocab, dict) and len(vocab) > 100:
                return True

            return False
        except:
            return False

    def _create_ct_tokenizer_from_punc_list(self, punc_list):
        """基于punc_list创建tokenizer，并与项目配置的标点集对齐。
        目标：确保包含 ['，','、','。','？','！']，顺序与 config.LABELS 的非 'O' 标签一致。
        """
        logger.info(f"🔧 基于punc_list创建tokenizer")
        logger.info(f"📋 punc_list内容: {punc_list}")

        # 依据配置生成目标标点列表（按LABELS顺序，排除 'O'）
        target_puncs = []
        for label in self.config.LABELS:
            punct = self.config.LABEL_TO_PUNCT.get(label, '')
            if punct and punct not in target_puncs:
                target_puncs.append(punct)

        # 规范化：优先使用目标标点集，然后补充 punc_list 中的其他标点（剔除占位符）
        normalized_puncs = list(target_puncs)
        if isinstance(punc_list, (list, tuple)):
            for p in punc_list:
                if p in ['<unk>', '_']:
                    continue
                if p and p not in normalized_puncs:
                    normalized_puncs.append(p)

        # 创建基于规范化标点集的字符级tokenizer
        class CTTokenizerFromPuncList:
            def __init__(self, normalized_puncs, raw_punc_list):
                self.punc_list = list(raw_punc_list) if isinstance(raw_punc_list, (list, tuple)) else []
                self.normalized_puncs = list(normalized_puncs)
                self.pad_token = '[PAD]'
                self.cls_token = '[CLS]'
                self.sep_token = '[SEP]'
                self.unk_token = '[UNK]'

                # 创建一个大的词汇表，包含常用汉字和标点符号
                self.vocab_size = 272727  # 与CT-Transformer保持一致

                # 特殊token映射
                self.special_tokens = {
                    self.pad_token: 0,
                    self.unk_token: 1,
                    self.cls_token: 101,  # 与BERT保持一致
                    self.sep_token: 102
                }

                # 基于规范化标点集建立映射，确保包含 '！'
                self.punc_mapping = {}
                base_id = 200
                for i, punc in enumerate(self.normalized_puncs):
                    self.punc_mapping[punc] = base_id + i

                logger.info(f"✅ 基于punc_list创建tokenizer，词汇表大小: {self.vocab_size}")
                logger.info(f"📋 标点符号映射: {self.punc_mapping}")

            def tokenize(self, text):
                if not text:
                    return []
                return list(str(text))

            def _char_to_id(self, char):
                if char in self.special_tokens:
                    return self.special_tokens[char]
                if char in self.punc_mapping:
                    return self.punc_mapping[char]
                char_code = ord(char)
                return min(max(char_code, 1000), self.vocab_size - 1)

            def __call__(self, text, truncation=True, padding='max_length', max_length=512, return_tensors='pt'):
                tokens = [self.cls_token] + self.tokenize(text)[:max_length-2] + [self.sep_token]
                input_ids = []
                for token in tokens:
                    input_ids.append(self._char_to_id(token))
                if len(input_ids) < max_length:
                    input_ids.extend([self.special_tokens[self.pad_token]] * (max_length - len(input_ids)))
                else:
                    input_ids = input_ids[:max_length]
                attention_mask = [1 if id != self.special_tokens[self.pad_token] else 0 for id in input_ids]

                if return_tensors == 'pt':
                    import torch
                    return {
                        'input_ids': torch.tensor([input_ids], dtype=torch.long),
                        'attention_mask': torch.tensor([attention_mask], dtype=torch.long)
                    }
                return {'input_ids': input_ids, 'attention_mask': attention_mask}

            def save_pretrained(self, path):
                import json, os
                os.makedirs(path, exist_ok=True)
                config = {
                    'tokenizer_type': 'CTTokenizerFromPuncList',
                    'pad_token': self.pad_token,
                    'cls_token': self.cls_token,
                    'sep_token': self.sep_token,
                    'unk_token': self.unk_token,
                    'vocab_size': self.vocab_size,
                    'special_tokens': self.special_tokens,
                    'punc_mapping': self.punc_mapping,
                    'punc_list': self.punc_list,
                    'normalized_puncs': self.normalized_puncs,
                }
                with open(os.path.join(path, 'tokenizer_config.json'), 'w', encoding='utf-8') as f:
                    json.dump(config, f, ensure_ascii=False, indent=2)

        return CTTokenizerFromPuncList(normalized_puncs, punc_list)

    def _create_ct_tokenizer_from_vocab(self, vocab):
        """基于词汇表创建tokenizer"""
        logger.info(f"🔧 基于词汇表创建tokenizer，词汇表类型: {type(vocab)}")

        # 这里可以根据vocab的具体格式来创建tokenizer
        # 暂时使用简化版本
        return self._create_compatible_tokenizer()

    def _create_compatible_tokenizer(self):
        """创建与CT-Transformer兼容的tokenizer"""
        class CTCompatibleTokenizer:
            def __init__(self):
                self.pad_token = '[PAD]'
                self.cls_token = '[CLS]'
                self.sep_token = '[SEP]'
                self.unk_token = '[UNK]'
                self.vocab_size = 272727  # CT-Transformer的词汇表大小
                self.special_tokens = {
                    self.pad_token: 0,
                    self.unk_token: 1,
                    self.cls_token: 101,
                    self.sep_token: 102
                }
                logger.info(f"✅ 创建兼容tokenizer，词汇表大小: {self.vocab_size}")

            def tokenize(self, text):
                # 字符级tokenization，与CT-Transformer保持一致
                if not text:
                    return []
                return list(str(text))

            def _char_to_id(self, char):
                """改进的字符到ID映射，确保稳定性"""
                if char in self.special_tokens:
                    return self.special_tokens[char]

                try:
                    char_code = ord(char)
                    # 中文字符范围
                    if 0x4e00 <= char_code <= 0x9fff:
                        char_id = char_code - 0x4e00 + 1000
                    # 数字字符
                    elif 0x0030 <= char_code <= 0x0039:
                        char_id = char_code - 0x0030 + 500
                    # 大写字母
                    elif 0x0041 <= char_code <= 0x005a:
                        char_id = char_code - 0x0041 + 600
                    # 小写字母
                    elif 0x0061 <= char_code <= 0x007a:
                        char_id = char_code - 0x0061 + 650
                    # 常见标点符号
                    elif char in '，。？！、；：""''（）【】':
                        char_id = 700 + ord(char) % 100
                    # 其他字符
                    else:
                        char_id = (char_code % (self.vocab_size - 1000)) + 1000

                    # 确保在有效范围内
                    return min(max(char_id, 103), self.vocab_size - 1)
                except Exception:
                    # 异常情况返回UNK
                    return self.special_tokens[self.unk_token]

            def __call__(self, text, truncation=True, padding='max_length', max_length=512, return_tensors='pt'):
                tokens = [self.cls_token] + self.tokenize(text)[:max_length-2] + [self.sep_token]
                input_ids = []
                for token in tokens:
                    token_id = self._char_to_id(token)
                    if token_id >= self.vocab_size:
                        token_id = self.special_tokens[self.unk_token]
                    input_ids.append(token_id)

                # 填充到指定长度
                while len(input_ids) < max_length:
                    input_ids.append(self.special_tokens[self.pad_token])

                attention_mask = [1 if id != self.special_tokens[self.pad_token] else 0 for id in input_ids]

                if return_tensors == 'pt':
                    return {
                        'input_ids': torch.tensor([input_ids], dtype=torch.long),
                        'attention_mask': torch.tensor([attention_mask], dtype=torch.long)
                    }
                return {'input_ids': input_ids, 'attention_mask': attention_mask}

            def save_pretrained(self, path):
                import json
                config = {
                    'tokenizer_type': 'CTCompatibleTokenizer',
                    'pad_token': self.pad_token,
                    'cls_token': self.cls_token,
                    'sep_token': self.sep_token,
                    'unk_token': self.unk_token,
                    'vocab_size': self.vocab_size
                }
                with open(os.path.join(path, 'tokenizer_config.json'), 'w', encoding='utf-8') as f:
                    json.dump(config, f, ensure_ascii=False, indent=2)

        return CTCompatibleTokenizer()

    def _create_ct_based_model(self):
        """创建基于CT-Transformer的分类模型"""
        import torch.nn as nn

        class CTTransformerForPunctuation(nn.Module):
            def __init__(self, ct_model, num_labels=6, hidden_size=768, config=None):
                super().__init__()
                self.ct_model = ct_model
                self.num_labels = num_labels
                self.hidden_size = hidden_size
                self.config = config

                # 增强的分类头设计
                enhanced_dropout = getattr(config, 'ENHANCED_DROPOUT', 0.3) if config else 0.3

                self.classifier = nn.Sequential(
                    nn.Linear(hidden_size, hidden_size // 2),
                    nn.ReLU(),
                    nn.Dropout(enhanced_dropout),
                    nn.Linear(hidden_size // 2, num_labels)
                )

                self.dropout = nn.Dropout(enhanced_dropout)
                # 仅在首次探测时打印一次编码器模块提示
                self._ct_encoder_modules_logged = False

                # 初始化分类头权重
                for module in self.classifier:
                    if isinstance(module, nn.Linear):
                        nn.init.normal_(module.weight, std=0.02)
                        nn.init.zeros_(module.bias)

                # FunASR AutoModel不支持标准的parameters()方法
                # 我们只训练分类头，保持CT-Transformer预训练权重不变
                logger.info("✅ CT-Transformer权重保持冻结，只训练分类头")
                logger.info(f"🔧 使用增强dropout率: {enhanced_dropout}")
                logger.info(f"🏗️ 分类头结构: {hidden_size} -> {hidden_size//2} -> {num_labels}")

                def _maybe_to_device(t):
                    return t.to(next(self.classifier.parameters()).device)

                def _stack_precomputed(features_list):
                    # features_list: list of [seq, hidden]
                    if features_list and isinstance(features_list[0], torch.Tensor):
                        batch = torch.stack(features_list, dim=0)
                        return _maybe_to_device(batch)
                    return None

                self._stack_precomputed = _stack_precomputed

            def _ct_encoder_hidden_states(self, input_ids):
                """尝试直接调用 CT-Transformer 编码器，返回 [batch, seq, hidden] 隐层。
                由于 funasr.AutoModel 内部结构不公开，这里仅做轻量探测；找不到可用接口则返回 None。
                """
                m = getattr(self.ct_model, 'model', None)
                if m is None:
                    return None
                # 常见候选属性名（仅探测存在性，不做重计算，避免显存/时间开销）
                candidates = ['encoder', 'backbone', 'punc_encoder', 'punctuator', 'net']
                found = []
                for attr in candidates:
                    if hasattr(m, attr):
                        found.append(attr)
                if found and not getattr(self, '_ct_encoder_modules_logged', False):
                    # 仅标记一次捕获结果，避免在进度条期间输出INFO干扰显示
                    self._ct_encoder_modules_found = found
                    self._ct_encoder_modules_logged = True
                # 暂未安全接通 -> 返回 None 交由后续路径处理
                return None

            def _get_hidden_by_hook(self, text: str, seq_len: int, device: torch.device):
                """通过在候选子模块上注册 forward hook，在一次 generate 前向中捕获隐层。
                返回形状 [seq_len, hidden_size] 的特征，捕获失败则返回 None。
                """
                core = getattr(self.ct_model, 'model', None)
                if core is None:
                    return None

                # 候选模块名优先包含以下关键词
                prefer = ['encoder', 'backbone', 'punc', 'punct', 'transformer']
                modules = []
                prefer_name = getattr(self.config, 'CT_ENCODER_HOOK_MODULE', '') or ''
                for name, mod in core.named_modules():
                    if prefer_name:
                        if name == prefer_name:
                            modules.append((name, mod))
                    else:
                        if any(k in name.lower() for k in prefer):
                            modules.append((name, mod))

                captured = {}
                def make_hook(key):
                    def hook(_m, _inp, out):
                        captured[key] = out
                    return hook

                # 逐个候选尝试捕获
                for name, mod in modules[:20]:  # 限制最多尝试若干个
                    handle = None
                    try:
                        handle = mod.register_forward_hook(make_hook(name))
                        # 触发一次前向（抑制funasr输出/进度）
                        import os
                        from contextlib import redirect_stdout, redirect_stderr
                        with open(os.devnull, 'w') as devnull:
                            with redirect_stdout(devnull), redirect_stderr(devnull):
                                _ = self.ct_model.generate(input=text)
                        out = captured.get(name, None)
                        if out is None:
                            continue
                        # 规范化输出为 Tensor
                        if isinstance(out, (list, tuple)):
                            out = out[-1]
                        if isinstance(out, dict):
                            out = out.get('hidden_states', None) or out.get('last_hidden_state', None)
                        if not isinstance(out, torch.Tensor):
                            continue
                        # 期望 [T, H] 或 [B, T, H]
                        if out.dim() == 2:  # [T, H]
                            feats = out
                        elif out.dim() == 3:  # [B, T, H]
                            feats = out[0]
                        else:
                            continue
                        # 维度自适应与对齐：确保输出为 [seq_len, hidden_size]
                        if feats.dim() == 1:
                            feats = feats.unsqueeze(0)
                        # 若隐层维度与分类头期望不一致，做线性投影
                        if feats.size(-1) != self.hidden_size:
                            proj = torch.nn.Linear(feats.size(-1), self.hidden_size, bias=False).to(feats.device)
                            with torch.no_grad():
                                feats = proj(feats)
                        # 对齐序列长度
                        T, H = feats.shape[-2], feats.shape[-1]
                        use_T = min(T, seq_len)
                        feats = feats[:use_T, :]
                        if use_T < seq_len:
                            pad = torch.zeros(seq_len - use_T, H, device=feats.device, dtype=feats.dtype)
                            feats = torch.cat([feats, pad], dim=0)
                        return feats.to(device)
                    except Exception:
                        pass
                    finally:
                        if handle is not None:
                            handle.remove()
                return None

            def forward(self, input_ids, attention_mask=None, labels=None, use_focal_loss=False, focal_loss_fn=None):
                """改进的前向传播，集成特征预计算和Focal Loss"""
                try:
                    batch_size, seq_len = input_ids.shape

                    # 方法1：优先直接使用CT-Transformer编码器隐层（方案B）
                    hidden_states = None
                    if getattr(self.config, 'USE_CT_ENCODER_FEATURES', False):
                        try:
                            hidden_states = self._ct_encoder_hidden_states(input_ids)
                        except Exception as e:
                            logger.warning(f"CT编码器隐层获取失败，回退到原有特征提取路径: {e}")

                    # 方法2：尝试使用（预计算或即时）CT特征提取
                    if hidden_states is None:
                        ct_features = self._extract_ct_features(input_ids, batch_size, seq_len)
                        if ct_features is not None:
                            hidden_states = ct_features

                    # 方法3：回退到基于嵌入的特征提取
                    if hidden_states is None:
                        hidden_states = self._create_embedding_features(input_ids, batch_size, seq_len)

                    # 应用dropout和分类
                    hidden_states = self.dropout(hidden_states)
                    logits = self.classifier(hidden_states)

                    loss = None
                    if labels is not None:
                        if use_focal_loss and focal_loss_fn is not None:
                            # 使用Focal Loss
                            loss = focal_loss_fn(logits.view(-1, logits.size(-1)), labels.view(-1))
                        else:
                            # 使用标准交叉熵损失
                            loss_fct = nn.CrossEntropyLoss(ignore_index=-100)
                            loss = loss_fct(logits.view(-1, logits.size(-1)), labels.view(-1))

                    class ModelOutput:
                        def __init__(self, loss, logits):
                            self.loss = loss
                            self.logits = logits

                    return ModelOutput(loss, logits)

                except Exception as e:
                    logger.error(f"模型前向传播失败: {e}")
                    # 最终回退
                    return self._fallback_forward(input_ids, attention_mask, labels)

            def _extract_ct_features(self, input_ids, batch_size, seq_len):
                """尝试从CT-Transformer提取特征"""
                try:
                    # 将 token ids 转回文本
                    input_texts = self._ids_to_texts(input_ids)

                    ct_outputs = []
                    for text in input_texts:
                        feature = self._get_hidden_by_hook(text, seq_len, input_ids.device)
                        if feature is None:
                            # 回退：随机特征（保持训练不中断）
                            feature = torch.randn(seq_len, self.hidden_size, device=input_ids.device)
                        ct_outputs.append(feature)

                    if ct_outputs:
                        return torch.stack(ct_outputs, dim=0)
                    return None

                except Exception as e:
                    logger.debug(f"CT特征提取失败: {e}")
                    return None

            def _ids_to_texts(self, input_ids):
                """将token ids转换为文本"""
                input_texts = []
                for i in range(input_ids.shape[0]):
                    text_chars = []
                    for token_id in input_ids[i]:
                        token_id = int(token_id.item())
                        if token_id == 0:  # PAD
                            break
                        elif token_id == 101:  # CLS
                            continue
                        elif token_id == 102:  # SEP
                            break
                        else:
                            # 改进的ID到字符映射
                            if 1000 <= token_id <= 22000:
                                char_code = token_id - 1000 + 0x4e00
                                if 0x4e00 <= char_code <= 0x9fff:
                                    text_chars.append(chr(char_code))
                                else:
                                    text_chars.append('？')
                            elif 500 <= token_id <= 509:
                                text_chars.append(str(token_id - 500))
                            elif 600 <= token_id <= 625:
                                text_chars.append(chr(token_id - 600 + ord('A')))
                            elif 650 <= token_id <= 675:
                                text_chars.append(chr(token_id - 650 + ord('a')))
                            else:
                                text_chars.append('？')
                    input_texts.append(''.join(text_chars))
                return input_texts

            def _create_embedding_features(self, input_ids, batch_size, seq_len):
                """创建基于嵌入的特征（回退方法）"""
                # 创建简单的嵌入层
                if not hasattr(self, 'embedding'):
                    vocab_size = 272727  # CT-Transformer词汇表大小
                    self.embedding = nn.Embedding(vocab_size, self.hidden_size, padding_idx=0)
                    self.embedding = self.embedding.to(input_ids.device)

                # 确保input_ids在有效范围内
                input_ids_clamped = torch.clamp(input_ids, 0, 272726)
                embeddings = self.embedding(input_ids_clamped)

                return embeddings

            def _fallback_forward(self, input_ids, attention_mask, labels):
                """回退的前向传播方法"""
                batch_size, seq_len = input_ids.shape
                # 创建简单的嵌入表示
                embeddings = torch.randn(batch_size, seq_len, self.hidden_size, device=input_ids.device)
                hidden_states = self.dropout(embeddings)
                logits = self.classifier(hidden_states)

                loss = None
                if labels is not None:
                    loss_fct = nn.CrossEntropyLoss(ignore_index=-100)
                    loss = loss_fct(logits.view(-1, logits.size(-1)), labels.view(-1))

                class ModelOutput:
                    def __init__(self, loss, logits):
                        self.loss = loss
                        self.logits = logits

                return ModelOutput(loss, logits)

            def save_pretrained(self, path):
                """保存模型，只保存可训练的部分（分类头/嵌入等），不包含CT基座。"""
                try:
                    os.makedirs(path, exist_ok=True)

                    # 保存模块自身的 state_dict（不包含 funasr 基座）
                    sd = self.state_dict()
                    torch.save(sd, os.path.join(path, 'pytorch_model.bin'))

                    # 记录训练时的结构与关键配置，便于推理侧还原
                    config = {
                        'model_type': 'CTTransformerForPunctuation',
                        'hidden_size': self.hidden_size,
                        'num_labels': self.num_labels,
                        'vocab_size': 272727,
                        'classifier_arch': 'two_layer',
                        'dropout_p': float(self.dropout.p),
                        'use_ct_encoder_features': bool(getattr(self.config, 'USE_CT_ENCODER_FEATURES', False)),
                        'ct_transformer_model': 'damo/punc_ct-transformer_zh-cn-common-vocab272727-pytorch',
                        'ct_transformer_version': 'v2.0.4'
                    }
                    with open(os.path.join(path, 'config.json'), 'w', encoding='utf-8') as f:
                        json.dump(config, f, ensure_ascii=False, indent=2)

                    # 企业级部署：保存为 best.pt 格式，便于直接加载
                    import datetime
                    model_checkpoint = {
                        'model_state_dict': sd,
                        'model_config': config,
                        'training_info': {
                            'save_time': datetime.datetime.now().isoformat(),
                            'pytorch_version': torch.__version__,
                        }
                    }

                    # 保存为 best.pt 文件，便于企业级部署
                    best_model_path = os.path.join(path, 'best.pt')
                    torch.save(model_checkpoint, best_model_path)

                    logger.info(f"✅ 模型已保存到: {path}")
                    logger.info(f"📦 企业级部署文件: {best_model_path}")
                except Exception as e:
                    logger.error(f"❌ 模型保存失败: {e}")
                    raise

        return CTTransformerForPunctuation(
            self.ct_model,
            num_labels=len(self.config.LABELS),
            config=self.config
        )

    def _check_real_data_exists(self):
        train_file = self.config.TRAIN_FILE
        valid_file = self.config.VALID_FILE
        train_exists = os.path.exists(train_file) and os.path.getsize(train_file) > 0
        valid_exists = os.path.exists(valid_file) and os.path.getsize(valid_file) > 0
        if train_exists and valid_exists:
            logger.info("✅ 检测到真实数据集，将使用真实数据进行训练")
            return True
        elif train_exists or valid_exists:
            logger.warning("⚠️ 检测到部分数据文件，建议检查数据完整性")
            return True
        else:
            logger.warning("❌ 未检测到真实数据集")
            return False

    def _load_real_data(self):
        processor = ImprovedDataProcessor()
        train_texts, train_labels = processor.load_data(self.config.TRAIN_FILE)
        valid_texts, valid_labels = processor.load_data(self.config.VALID_FILE)
        if not train_texts or not valid_texts:
            raise ValueError("真实数据集加载失败或数据为空")
        return train_texts, train_labels, valid_texts, valid_labels

    def train(self):
        print("\n🏋️ 开始进行模型微调训练...")
        self.config.create_dirs()

        if self._check_real_data_exists():
            train_texts, train_labels, valid_texts, valid_labels = self._load_real_data()
            print(f"📊 使用真实数据集 - 训练数据: {len(train_texts)} 条, 验证数据: {len(valid_texts)} 条")
        else:
            raise FileNotFoundError(
                f"未找到真实训练数据！请确保以下文件存在且不为空：{self.config.TRAIN_FILE} 和 {self.config.VALID_FILE}"
            )

        # 预计算CT特征（如果启用）
        if self.feature_precomputer:
            print("⚡ 开始预计算CT特征...")
            all_texts = train_texts + valid_texts
            self.precomputed_features = self.feature_precomputer.precompute_features(all_texts)
            print(f"✅ 预计算完成，缓存了 {len(self.precomputed_features)} 个文本的特征")

        train_dataset = PunctuationDataset(train_texts, train_labels, self.tokenizer, self.config.MAX_LENGTH)
        valid_dataset = PunctuationDataset(valid_texts, valid_labels, self.tokenizer, self.config.MAX_LENGTH)

        # DataLoader 性能优化：多进程、锁页内存
        train_loader = DataLoader(
            train_dataset,
            batch_size=self.config.BATCH_SIZE,
            shuffle=True,
            num_workers=8,
            pin_memory=True,
            persistent_workers=True,
            prefetch_factor=2,
        )
        valid_loader = DataLoader(
            valid_dataset,
            batch_size=self.config.BATCH_SIZE,
            shuffle=False,
            num_workers=8,
            pin_memory=True,
            persistent_workers=True,
            prefetch_factor=2,
        )

        optimizer = AdamW(self.model.parameters(), lr=self.config.LEARNING_RATE, weight_decay=self.config.WEIGHT_DECAY)
        total_steps = len(train_loader) * self.config.NUM_EPOCHS
        scheduler = get_linear_schedule_with_warmup(optimizer, num_warmup_steps=int(total_steps * self.config.WARMUP_RATIO), num_training_steps=total_steps)

        best_valid_f1 = 0.0
        best_epoch = 0
        best_model_state = None

        # 精简训练日志，避免与进度条交错
        for epoch in range(self.config.NUM_EPOCHS):
            # 每轮开始的标题行（简洁、单行）
            print(f"Epoch {epoch + 1}/{self.config.NUM_EPOCHS}")

            # 训练阶段：直接运行进度条（desc即为“训练中: ”），不再额外打印提示
            train_loss = self._train_epoch(train_loader, optimizer, scheduler)

            # 验证阶段：直接运行进度条（desc即为“验证中: ”）
            valid_loss, valid_metrics = self._validate_epoch(valid_loader)

            # 统一打印本轮统计信息（进度条已结束）
            print(f"训练损失: {train_loss:.4f}")
            print(f"验证损失: {valid_loss:.4f}")
            print(f"验证F1: {valid_metrics['f1']:.4f}")
            print(f"验证准确率: {valid_metrics['accuracy']:.4f}")

            if valid_metrics['f1'] > best_valid_f1:
                best_valid_f1 = valid_metrics['f1']
                best_epoch = epoch + 1
                best_model_state = self.model.state_dict().copy()
                print(f"🎯 发现更好模型，F1: {best_valid_f1:.4f}")

            if (epoch + 1) % 10 == 0:
                print(f"📈 当前最佳: Epoch {best_epoch}, F1: {best_valid_f1:.4f}")

        if best_model_state is not None:
            print(f"\n💾 保存最优模型...")
            self.model.load_state_dict(best_model_state)
            self._save_best_model({'f1': best_valid_f1}, best_epoch)
            print(f"✅ 最优模型已保存到: {self.config.FINETUNED_MODEL_DIR}")
        else:
            print("⚠️ 未找到有效的最佳模型")

        print(f"\n🎉 训练完成！最佳F1分数: {best_valid_f1:.4f}")
        return best_valid_f1

    def _train_epoch(self, train_loader, optimizer, scheduler):
        self.model.train()
        total_loss = 0.0
        accumulation_steps = getattr(self.config, 'GRADIENT_ACCUMULATION_STEPS', 4)
        max_grad_norm = getattr(self.config, 'MAX_GRAD_NORM', 1.0)

        # 使用简洁进度条，避免与日志交错；保留完成后的进度条
        progress_bar = tqdm(train_loader, desc="训练中", leave=True, dynamic_ncols=True)
        optimizer.zero_grad()

        for step, batch in enumerate(progress_bar):
            input_ids = batch['input_ids'].to(self.device)
            attention_mask = batch['attention_mask'].to(self.device)
            labels = batch['labels'].to(self.device)

            # 混合精度（BF16）
            use_focal = self.focal_loss is not None
            with torch.autocast(device_type='cuda', dtype=torch.bfloat16, enabled=(self.device.type == 'cuda')):
                outputs = self.model(
                    input_ids=input_ids,
                    attention_mask=attention_mask,
                    labels=labels,
                    use_focal_loss=use_focal,
                    focal_loss_fn=self.focal_loss
                )
                loss = outputs.loss

            # 梯度累积
            loss = loss / accumulation_steps
            loss.backward()
            total_loss += loss.item() * accumulation_steps

            if (step + 1) % accumulation_steps == 0:
                # 梯度裁剪
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_grad_norm)
                optimizer.step()
                scheduler.step()
                optimizer.zero_grad()

            # 更详细的进度显示
            progress_bar.set_postfix({
                'loss': f'{loss.item() * accumulation_steps:.4f}',
                'lr': f'{scheduler.get_last_lr()[0]:.2e}',
                'step': f'{step+1}/{len(train_loader)}'
            })

        # 处理最后不完整的累积步骤
        if (step + 1) % accumulation_steps != 0:
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_grad_norm)
            optimizer.step()
            scheduler.step()
            optimizer.zero_grad()

        return total_loss / len(train_loader)

    def _validate_epoch(self, valid_loader):
        self.model.eval()
        total_loss = 0.0
        all_predictions = []
        all_labels = []
        with torch.no_grad():
            for batch in tqdm(valid_loader, desc="验证中"):
                input_ids = batch['input_ids'].to(self.device)
                attention_mask = batch['attention_mask'].to(self.device)
                labels = batch['labels'].to(self.device)

                outputs = self.model(input_ids=input_ids, attention_mask=attention_mask, labels=labels)
                loss = outputs.loss
                total_loss += loss.item()

                predictions = torch.argmax(outputs.logits, dim=-1)
                mask = labels != -100
                valid_predictions = predictions[mask].cpu().numpy()
                valid_labels = labels[mask].cpu().numpy()
                all_predictions.extend(valid_predictions)
                all_labels.extend(valid_labels)

        avg_loss = total_loss / len(valid_loader)
        metrics = self._compute_metrics(all_predictions, all_labels)
        return avg_loss, metrics

    def _compute_metrics(self, predictions, labels):
        accuracy = accuracy_score(labels, predictions)
        precision, recall, f1, _ = precision_recall_fscore_support(labels, predictions, average='weighted', zero_division=0)
        return {'accuracy': accuracy, 'precision': precision, 'recall': recall, 'f1': f1}

    def _save_best_model(self, metrics, epoch):
        self._ensure_dir_exists(self.config.FINETUNED_MODEL_DIR)
        self.model.save_pretrained(self.config.FINETUNED_MODEL_DIR)
        self.tokenizer.save_pretrained(self.config.FINETUNED_MODEL_DIR)
        training_info = {
            'model_type': 'CT-Transformer-Finetuned',
            'base_model': 'damo/punc_ct-transformer_zh-cn-common-vocab272727-pytorch',
            'model_version': 'v2.0.4',
            'labels': self.config.LABELS,
            'num_labels': len(self.config.LABELS),
            'max_length': self.config.MAX_LENGTH,
            'batch_size': self.config.BATCH_SIZE,
            'learning_rate': self.config.LEARNING_RATE,
            'num_epochs': self.config.NUM_EPOCHS,
            'best_epoch': epoch,
            'device': str(self.device),
            'training_time': time.strftime('%Y-%m-%d %H:%M:%S'),
            'best_metrics': metrics,
            'total_epochs_trained': self.config.NUM_EPOCHS
        }
        with open(self.config.TRAINING_ARGS_FILE, 'w', encoding='utf-8') as f:
            json.dump(training_info, f, ensure_ascii=False, indent=2)
        logger.info(f"💾 最优模型已保存到: {self.config.FINETUNED_MODEL_DIR}")

    def _ensure_dir_exists(self, dir_path):
        if not os.path.exists(dir_path):
            try:
                os.makedirs(dir_path, exist_ok=True)
                logger.info(f"创建目录: {dir_path}")
            except OSError as e:
                logger.error(f"创建目录失败 {dir_path}: {e}")
                raise
        else:
            pass

def main():
    print("🚀 中文标点符号预测模型微调训练")
    print("=" * 50)
    try:
        import funasr, torch
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        return
    try:
        trainer = PunctuationTrainer()
        best_f1 = trainer.train()
        print(f"\n🎉 微调训练完成！最佳F1分数: {best_f1:.4f}")
        config = PunctuationConfig()
        print(f"📁 微调模型保存位置: {config.FINETUNED_MODEL_DIR}")
        print(f"📋 训练参数文件: {config.TRAINING_ARGS_FILE}")
    except Exception as e:
        logger.error(f"训练失败: {e}")
        print(f"❌ 训练失败: {e}")
        import traceback; traceback.print_exc()

if __name__ == "__main__":
    main()
